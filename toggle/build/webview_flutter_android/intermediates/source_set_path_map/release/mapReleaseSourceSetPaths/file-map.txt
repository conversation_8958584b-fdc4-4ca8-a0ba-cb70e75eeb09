io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-lifecycle-viewmodel-savedstate-2.7.0-0 /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-lifecycle-viewmodel-2.7.0-1 /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-tracing-1.2.0-2 /Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-annotation-experimental-1.4.1-3 /Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-core-runtime-2.2.0-4 /Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-5 /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-webkit-1.12.1-6 /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-profileinstaller-1.3.1-7 /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-window-1.2.0-8 /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-lifecycle-process-2.7.0-9 /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-lifecycle-livedata-core-2.7.0-10 /Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-savedstate-1.2.1-11 /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-core-1.0.0-12 /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-lifecycle-livedata-2.7.0-13 /Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-activity-1.8.1-14 /Users/<USER>/.gradle/caches/transforms-3/8f8301fae71d6834f5847f09ec4fa34f/transformed/jetified-activity-1.8.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-lifecycle-runtime-2.7.0-15 /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-startup-runtime-1.1.1-16 /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-core-ktx-1.13.1-17 /Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-window-java-1.2.0-18 /Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-jetified-lifecycle-livedata-core-ktx-2.7.0-19 /Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-20 /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/res
io.flutter.plugins.webviewflutter.webview_flutter_android-main-21 /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.2.0/android/src/main/res
io.flutter.plugins.webviewflutter.webview_flutter_android-release-22 /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.2.0/android/src/release/res
io.flutter.plugins.webviewflutter.webview_flutter_android-pngs-23 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/generated/res/pngs/release
io.flutter.plugins.webviewflutter.webview_flutter_android-resValues-24 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/generated/res/resValues/release
io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-25 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/incremental/release/mergeReleaseResources/merged.dir
io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-26 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/incremental/release/mergeReleaseResources/stripped.dir
io.flutter.plugins.webviewflutter.webview_flutter_android-release-27 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/merged_res/release/mergeReleaseResources
