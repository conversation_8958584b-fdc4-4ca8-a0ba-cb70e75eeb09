{"logs": [{"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-24:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}}]}, {"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}}]}]}