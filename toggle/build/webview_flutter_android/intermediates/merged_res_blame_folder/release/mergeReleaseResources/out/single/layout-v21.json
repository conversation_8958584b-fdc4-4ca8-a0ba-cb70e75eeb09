[{"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-5:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-5:/layout-v21/notification_template_icon_group.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-5:/layout-v21/notification_action_tombstone.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-5:/layout-v21/notification_action.xml"}]