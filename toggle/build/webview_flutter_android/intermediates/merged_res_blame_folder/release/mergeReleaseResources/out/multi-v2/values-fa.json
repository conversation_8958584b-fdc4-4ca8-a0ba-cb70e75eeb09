{"logs": [{"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}}]}, {"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-24:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}}]}]}