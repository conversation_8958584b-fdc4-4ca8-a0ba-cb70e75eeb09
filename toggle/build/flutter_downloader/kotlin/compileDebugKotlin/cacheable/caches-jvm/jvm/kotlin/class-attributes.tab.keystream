*vn.hunghd.flutterdownloader.DownloadStatus(vn.hunghd.flutterdownloader.DownloadTask*vn.hunghd.flutterdownloader.DownloadWorker4vn.hunghd.flutterdownloader.DownloadWorker.Companion2vn.hunghd.flutterdownloader.DownloadedFileProvider8vn.hunghd.flutterdownloader.FlutterDownloaderInitializerBvn.hunghd.flutterdownloader.FlutterDownloaderInitializer.Companion3vn.hunghd.flutterdownloader.FlutterDownloaderPlugin=vn.hunghd.flutterdownloader.FlutterDownloaderPlugin.Companion'vn.hunghd.flutterdownloader.IntentUtils#vn.hunghd.flutterdownloader.TaskDao(vn.hunghd.flutterdownloader.TaskDbHelper2vn.hunghd.flutterdownloader.TaskDbHelper.Companion%vn.hunghd.flutterdownloader.TaskEntry                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             