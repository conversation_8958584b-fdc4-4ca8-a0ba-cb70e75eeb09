com.example.cepron.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-0 /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
com.example.cepron.app-lifecycle-viewmodel-2.7.0-1 /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/res
com.example.cepron.app-jetified-tracing-1.2.0-2 /Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/res
com.example.cepron.app-media-1.7.0-3 /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/res
com.example.cepron.app-jetified-annotation-experimental-1.4.1-4 /Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/res
com.example.cepron.app-jetified-ads-adservices-java-1.0.0-beta05-5 /Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05/res
com.example.cepron.app-browser-1.8.0-6 /Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/res
com.example.cepron.app-core-runtime-2.2.0-7 /Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/res
com.example.cepron.app-jetified-firebase-messaging-24.0.3-8 /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/res
com.example.cepron.app-core-1.13.1-9 /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res
com.example.cepron.app-sqlite-framework-2.3.0-10 /Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0/res
com.example.cepron.app-webkit-1.12.1-11 /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/res
com.example.cepron.app-jetified-profileinstaller-1.3.1-12 /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/res
com.example.cepron.app-jetified-play-services-measurement-api-22.1.2-13 /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/res
com.example.cepron.app-jetified-window-1.2.0-14 /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/res
com.example.cepron.app-jetified-lifecycle-process-2.7.0-15 /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/res
com.example.cepron.app-jetified-android-pdf-viewer-3.2.0-beta.3-16 /Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/res
com.example.cepron.app-jetified-play-services-basement-18.4.0-17 /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/res
com.example.cepron.app-jetified-ads-adservices-1.0.0-beta05-18 /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/res
com.example.cepron.app-jetified-activity-1.9.1-19 /Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/res
com.example.cepron.app-lifecycle-livedata-core-2.7.0-20 /Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/res
com.example.cepron.app-sqlite-2.3.0-21 /Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0/res
com.example.cepron.app-jetified-savedstate-1.2.1-22 /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/res
com.example.cepron.app-jetified-core-1.0.0-23 /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/res
com.example.cepron.app-lifecycle-livedata-2.7.0-24 /Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/res
com.example.cepron.app-jetified-media3-exoplayer-1.4.1-25 /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/res
com.example.cepron.app-jetified-datastore-preferences-1.0.0-26 /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/res
com.example.cepron.app-jetified-datastore-1.0.0-27 /Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0/res
com.example.cepron.app-lifecycle-runtime-2.7.0-28 /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/res
com.example.cepron.app-localbroadcastmanager-1.1.0-29 /Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0/res
com.example.cepron.app-jetified-lifecycle-service-2.7.0-30 /Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0/res
com.example.cepron.app-jetified-startup-runtime-1.1.1-31 /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/res
com.example.cepron.app-jetified-core-ktx-1.13.1-32 /Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/res
com.example.cepron.app-jetified-window-java-1.2.0-33 /Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/res
com.example.cepron.app-jetified-firebase-common-21.0.0-34 /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/res
com.example.cepron.app-jetified-room-ktx-2.5.0-35 /Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0/res
com.example.cepron.app-room-runtime-2.5.0-36 /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/res
com.example.cepron.app-jetified-lifecycle-livedata-core-ktx-2.7.0-37 /Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
com.example.cepron.app-work-runtime-2.9.0-38 /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/res
com.example.cepron.app-jetified-play-services-base-18.5.0-39 /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/res
com.example.cepron.app-fragment-1.7.1-40 /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/res
com.example.cepron.app-debug-41 /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/res
com.example.cepron.app-main-42 /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/res
com.example.cepron.app-google-services-43 /Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/google-services/debug
com.example.cepron.app-pngs-44 /Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/pngs/debug
com.example.cepron.app-resValues-45 /Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/resValues/debug
com.example.cepron.app-mergeDebugResources-46 /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir
com.example.cepron.app-mergeDebugResources-47 /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/incremental/debug/mergeDebugResources/stripped.dir
com.example.cepron.app-debug-48 /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/merged_res/debug/mergeDebugResources
com.example.cepron.app-debug-49 /Users/<USER>/Desktop/Toggle/toggle/build/app_settings/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-50 /Users/<USER>/Desktop/Toggle/toggle/build/audio_session/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-51 /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-52 /Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-53 /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-54 /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-55 /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-56 /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-57 /Users/<USER>/Desktop/Toggle/toggle/build/flutter_native_splash/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-58 /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-59 /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-60 /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-61 /Users/<USER>/Desktop/Toggle/toggle/build/just_audio/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-62 /Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-63 /Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-64 /Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-65 /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-66 /Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-67 /Users/<USER>/Desktop/Toggle/toggle/build/sqflite/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-68 /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-69 /Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-70 /Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.cepron.app-debug-71 /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/packaged_res/debug/packageDebugResources
