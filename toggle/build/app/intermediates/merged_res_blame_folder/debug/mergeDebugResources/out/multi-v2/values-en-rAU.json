{"logs": [{"outputFile": "com.example.cepron.app-mergeDebugResources-46:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "872,942,1004,1069,1133,1210,1275,1365,1450", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "937,999,1064,1128,1205,1270,1360,1445,1514"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,22", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,1824", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,1920"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,19,20,21", "startColumns": "4,4,4,4", "startOffsets": "774,1519,1616,1725", "endColumns": "97,96,108,98", "endOffsets": "867,1611,1720,1819"}}]}]}