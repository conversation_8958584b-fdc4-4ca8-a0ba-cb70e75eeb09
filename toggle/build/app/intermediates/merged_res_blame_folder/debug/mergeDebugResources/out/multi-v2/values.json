{"logs": [{"outputFile": "com.example.cepron.app-mergeDebugResources-46:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "61,62,63,64,81,82,197,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2193,2251,2317,2380,3666,3737,12009,12723,12790,12869", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2246,2312,2375,2437,3732,3804,12072,12785,12864,12933"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "198,199,200,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12077,12147,12209,12274,12338,12415,12480,12570,12654", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "12142,12204,12269,12333,12410,12475,12565,12649,12718"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "163", "startColumns": "4", "startOffsets": "8613", "endColumns": "42", "endOffsets": "8651"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "171", "startColumns": "4", "startOffsets": "9082", "endColumns": "82", "endOffsets": "9160"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,140,255,261,422,430,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,7398,16243,16438,21742,22024,22638", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,140,260,265,429,444,460", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,7453,16433,16591,22019,22633,23287"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "141,146,167,336,341", "startColumns": "4,4,4,4,4", "startOffsets": "7458,7689,8820,19512,19682", "endLines": "141,146,167,340,344", "endColumns": "56,64,63,24,24", "endOffsets": "7510,7749,8879,19677,19826"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "169,187", "startColumns": "4,4", "startOffsets": "8944,10716", "endColumns": "67,166", "endOffsets": "9007,10878"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "166", "startColumns": "4", "startOffsets": "8770", "endColumns": "49", "endOffsets": "8815"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "67,68,69,70,71,72,73,74,179,180,181,182,183,184,185,186,188,189,190,191,192,193,194,195,196,384,403", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2573,2663,2743,2833,2923,3003,3084,3164,9676,9781,9962,10087,10194,10374,10497,10613,10883,11071,11176,11357,11482,11657,11805,11868,11930,20680,21325", "endLines": "67,68,69,70,71,72,73,74,179,180,181,182,183,184,185,186,188,189,190,191,192,193,194,195,196,396,421", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2658,2738,2828,2918,2998,3079,3159,3239,9776,9957,10082,10189,10369,10492,10608,10711,11066,11171,11352,11477,11652,11800,11863,11925,12004,20990,21737"}}, {"source": "/Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/google-services/debug/values/values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,577", "endColumns": "81,103,108,119,106,75", "endOffsets": "132,236,345,465,572,648"}, "to": {"startLines": "219,220,221,222,223,224", "startColumns": "4,4,4,4,4,4", "startOffsets": "13674,13756,13860,13969,14089,14196", "endColumns": "81,103,108,119,106,75", "endOffsets": "13751,13855,13964,14084,14191,14267"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "78,79,80,144,168,242,244,245,250,252", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3433,3522,3593,7585,8884,15282,15458,15580,15842,16037", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "3517,3588,3661,7642,8939,15343,15575,15636,15903,16099"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/res/values/values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "55,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "1773,1838,1908,1972", "endColumns": "64,69,63,60", "endOffsets": "1833,1903,1967,2028"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,59,60,65,66,76,77,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,142,143,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,170,172,173,174,175,176,177,178,225,237,238,243,246,251,253,254,266,272,282,315,345,378", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,2033,2105,2442,2507,3301,3370,3809,3879,3947,4019,4089,4150,4224,4297,4358,4419,4481,4545,4607,4668,4736,4836,4896,4962,5035,5104,5161,5213,5275,5347,5423,5488,5547,5606,5666,5726,5786,5846,5906,5966,6026,6086,6146,6206,6265,6325,6385,6445,6505,6565,6625,6685,6745,6805,6865,6924,6984,7044,7103,7162,7221,7280,7339,7515,7550,7754,7809,7872,7927,7985,8043,8104,8167,8224,8275,8325,8386,8443,8509,8543,8578,9012,9165,9232,9304,9373,9442,9516,9588,14272,14964,15081,15348,15641,15908,16104,16176,16596,16799,17100,18831,19831,20513", "endLines": "25,59,60,65,66,76,77,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,142,143,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,170,172,173,174,175,176,177,178,225,237,241,243,249,251,253,254,271,281,314,335,377,383", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,2100,2188,2502,2568,3365,3428,3874,3942,4014,4084,4145,4219,4292,4353,4414,4476,4540,4602,4663,4731,4831,4891,4957,5030,5099,5156,5208,5270,5342,5418,5483,5542,5601,5661,5721,5781,5841,5901,5961,6021,6081,6141,6201,6260,6320,6380,6440,6500,6560,6620,6680,6740,6800,6860,6919,6979,7039,7098,7157,7216,7275,7334,7393,7545,7580,7804,7867,7922,7980,8038,8099,8162,8219,8270,8320,8381,8438,8504,8538,8573,8608,9077,9227,9299,9368,9437,9511,9583,9671,14338,15076,15277,15453,15837,16032,16171,16238,16794,17095,18826,19507,20508,20675"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "210", "startColumns": "4", "startOffsets": "12938", "endColumns": "81", "endOffsets": "13015"}}, {"source": "/Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,239,323,401,475,559,633", "endColumns": "77,105,83,77,73,83,73,75", "endOffsets": "128,234,318,396,470,554,628,704"}, "to": {"startLines": "211,212,213,214,215,216,217,218", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13020,13098,13204,13288,13366,13440,13524,13598", "endColumns": "77,105,83,77,73,83,73,75", "endOffsets": "13093,13199,13283,13361,13435,13519,13593,13669"}}, {"source": "/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/res/values/ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "3244", "endColumns": "56", "endOffsets": "3296"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "145,164", "startColumns": "4,4", "startOffsets": "7647,8656", "endColumns": "41,59", "endOffsets": "7684,8711"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "165", "startColumns": "4", "startOffsets": "8716", "endColumns": "53", "endOffsets": "8765"}}, {"source": "/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,18", "startColumns": "4,4", "startOffsets": "173,1089", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "747,1253"}, "to": {"startLines": "226,234", "startColumns": "4,4", "startOffsets": "14343,14795", "endLines": "233,236", "endColumns": "12,12", "endOffsets": "14790,14959"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "24", "endOffsets": "380"}, "to": {"startLines": "397", "startColumns": "4", "startOffsets": "20995", "endLines": "402", "endColumns": "24", "endOffsets": "21320"}}]}]}