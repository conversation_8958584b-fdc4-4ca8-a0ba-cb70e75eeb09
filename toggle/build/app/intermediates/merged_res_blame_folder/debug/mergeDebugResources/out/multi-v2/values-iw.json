{"logs": [{"outputFile": "com.example.cepron.app-mergeDebugResources-46:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/res/values-iw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1717", "endColumns": "117", "endOffsets": "1830"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2914,2979,3038,3105,3170,3244,3306,3386,3466", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "2974,3033,3100,3165,3239,3301,3381,3461,3523"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/res/values-iw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,855,1009,1134,1238,1377,1502,1614,1835,1971,2075,2220,2343,2477,2622,2682,2742", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "850,1004,1129,1233,1372,1497,1609,1712,1966,2070,2215,2338,2472,2617,2677,2737,2818"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "2823,3528,3628,3734", "endColumns": "90,99,105,101", "endOffsets": "2909,3623,3729,3831"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,3836", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,3932"}}]}]}