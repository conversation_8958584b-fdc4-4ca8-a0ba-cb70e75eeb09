<dependencies>
  <compile
      roots="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified@jar,com.google.firebase:firebase-analytics:22.1.2@aar,com.google.android.gms:play-services-measurement-api:22.1.2@aar,:@@:flutter_downloader::release,:@@:shared_preferences_android::release,:@@:webview_flutter_android::release,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,:@@:app_settings::release,:@@:audio_session::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:firebase_messaging::release,:@@:firebase_core::release,:@@:flutter_local_notifications::release,:@@:flutter_pdfview::release,:@@:flutter_plugin_android_lifecycle::release,:@@:image_picker_android::release,:@@:just_audio::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:share_plus::release,:@@:sqflite::release,:@@:url_launcher_android::release,:@@:video_player_android::release,:@@:wakelock_plus::release,io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar,com.google.android.gms:play-services-measurement:22.1.2@aar,com.google.android.gms:play-services-measurement-sdk:22.1.2@aar,com.google.android.gms:play-services-measurement-impl:22.1.2@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar,com.google.android.gms:play-services-measurement-base:22.1.2@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.9.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20@jar,androidx.multidex:multidex:2.0.0@aar,io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:33.0.0-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.guava:failureaccess:1.0.2@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.41.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.j2objc:j2objc-annotations:2.8@jar">
    <dependency
        name="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
        simpleName="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.1.2@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name=":@@:flutter_downloader::release"
        simpleName="vn.hunghd.flutterdownloader:flutter_downloader"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="io.flutter.plugins.webviewflutter:webview_flutter_android"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="com.spencerccf.app_settings:app_settings"/>
    <dependency
        name=":@@:audio_session::release"
        simpleName="com.ryanheise.audio_session:audio_session"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="dev.fluttercommunity.plus.connectivity:connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="dev.fluttercommunity.plus.device_info:device_info_plus"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="io.flutter.plugins.firebasemessaging:firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="io.flutter.plugins.firebase.core:firebase_core"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="com.dexterous.flutterlocalnotifications:flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_pdfview::release"
        simpleName="io.endigo.plugings.pdfview:flutter_pdfview"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="io.flutter.plugins.imagepicker:image_picker_android"/>
    <dependency
        name=":@@:just_audio::release"
        simpleName="com.ryanheise.just_audio:just_audio"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="dev.fluttercommunity.plus.packageinfo:package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="dev.fluttercommunity.plus.share:share_plus"/>
    <dependency
        name=":@@:sqflite::release"
        simpleName="com.tekartik.sqflite:sqflite"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="io.flutter.plugins.urllauncher:url_launcher_android"/>
    <dependency
        name=":@@:video_player_android::release"
        simpleName="io.flutter.plugins.videoplayer:video_player_android"/>
    <dependency
        name=":@@:wakelock_plus::release"
        simpleName="dev.fluttercommunity.plus.wakelock:wakelock_plus"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.9.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.multidex:multidex:2.0.0@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:33.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.41.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified@jar,:@@:flutter_downloader::release,:@@:shared_preferences_android::release,:@@:webview_flutter_android::release,:@@:image_picker_android::release,:@@:firebase_messaging::release,:@@:firebase_core::release,com.google.firebase:firebase-messaging:24.0.3@aar,com.google.firebase:firebase-analytics:22.1.2@aar,com.google.android.gms:play-services-measurement-api:22.1.2@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,androidx.work:work-runtime:2.9.0@aar,:@@:app_settings::release,:@@:just_audio::release,:@@:audio_session::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:flutter_local_notifications::release,:@@:flutter_pdfview::release,:@@:flutter_plugin_android_lifecycle::release,:@@:wakelock_plus::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:share_plus::release,:@@:sqflite::release,:@@:url_launcher_android::release,:@@:video_player_android::release,io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar,androidx.media:media:1.7.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.android.gms:play-services-measurement:22.1.2@aar,com.google.android.gms:play-services-measurement-sdk:22.1.2@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-measurement-impl:22.1.2@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar,com.google.android.gms:play-services-measurement-base:22.1.2@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.datastore:datastore-preferences:1.0.0@aar,androidx.datastore:datastore:1.0.0@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.datastore:datastore-preferences-core:1.0.0@jar,androidx.datastore:datastore-core:1.0.0@jar,androidx.activity:activity:1.9.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-service:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.core:core-ktx:1.13.1@aar,com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar,androidx.media3:media3-extractor:1.4.1@aar,androidx.media3:media3-container:1.4.1@aar,androidx.media3:media3-datasource:1.4.1@aar,androidx.media3:media3-decoder:1.4.1@aar,androidx.media3:media3-database:1.4.1@aar,androidx.media3:media3-common:1.4.1@aar,androidx.media3:media3-exoplayer-dash:1.4.1@aar,androidx.media3:media3-exoplayer-hls:1.4.1@aar,androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar,androidx.media3:media3-exoplayer-rtsp:1.4.1@aar,androidx.media3:media3-exoplayer:1.4.1@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.12.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.room:room-ktx:2.5.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,androidx.room:room-runtime:2.5.0@aar,androidx.room:room-common:2.5.0@jar,androidx.sqlite:sqlite-framework:2.3.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-components:18.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.sqlite:sqlite:2.3.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20@jar,androidx.multidex:multidex:2.0.0@aar,io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar,com.google.code.gson:gson:2.12.0@jar,org.jetbrains:annotations:23.0.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.firebase:firebase-annotations:16.2.0@jar,com.google.guava:guava:33.0.0-android@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.github.mhiew:pdfium-android:1.9.2@aar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.2@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.41.0@jar">
    <dependency
        name="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
        simpleName="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar"/>
    <dependency
        name=":@@:flutter_downloader::release"
        simpleName="vn.hunghd.flutterdownloader:flutter_downloader"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="io.flutter.plugins.webviewflutter:webview_flutter_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="io.flutter.plugins.imagepicker:image_picker_android"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="io.flutter.plugins.firebasemessaging:firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="io.flutter.plugins.firebase.core:firebase_core"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.0.3@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.1.2@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="com.spencerccf.app_settings:app_settings"/>
    <dependency
        name=":@@:just_audio::release"
        simpleName="com.ryanheise.just_audio:just_audio"/>
    <dependency
        name=":@@:audio_session::release"
        simpleName="com.ryanheise.audio_session:audio_session"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="dev.fluttercommunity.plus.connectivity:connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="dev.fluttercommunity.plus.device_info:device_info_plus"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="com.dexterous.flutterlocalnotifications:flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_pdfview::release"
        simpleName="io.endigo.plugings.pdfview:flutter_pdfview"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:wakelock_plus::release"
        simpleName="dev.fluttercommunity.plus.wakelock:wakelock_plus"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="dev.fluttercommunity.plus.packageinfo:package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="dev.fluttercommunity.plus.share:share_plus"/>
    <dependency
        name=":@@:sqflite::release"
        simpleName="com.tekartik.sqflite:sqflite"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="io.flutter.plugins.urllauncher:url_launcher_android"/>
    <dependency
        name=":@@:video_player_android::release"
        simpleName="io.flutter.plugins.videoplayer:video_player_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.datastore:datastore-preferences:1.0.0@aar"
        simpleName="androidx.datastore:datastore-preferences"/>
    <dependency
        name="androidx.datastore:datastore:1.0.0@aar"
        simpleName="androidx.datastore:datastore"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-preferences-core"/>
    <dependency
        name="androidx.datastore:datastore-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-core"/>
    <dependency
        name="androidx.activity:activity:1.9.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar"
        simpleName="com.github.mhiew:android-pdf-viewer"/>
    <dependency
        name="androidx.media3:media3-extractor:1.4.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.4.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.4.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.4.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.4.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.4.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-hls"/>
    <dependency
        name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-smoothstreaming"/>
    <dependency
        name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-rtsp"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.12.1@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.room:room-ktx:2.5.0@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.multidex:multidex:2.0.0@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="com.google.code.gson:gson:2.12.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.guava:guava:33.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.github.mhiew:pdfium-android:1.9.2@aar"
        simpleName="com.github.mhiew:pdfium-android"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.41.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
  </package>
</dependencies>
