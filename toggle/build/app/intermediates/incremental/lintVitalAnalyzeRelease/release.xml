<variant
    name="release"
    package="com.example.cepron"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/merged_manifest/release/processReleaseMainManifest/AndroidManifest.xml"
    proguardFiles="/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.3.2:/Users/<USER>/development/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro:proguard-rules.pro"
    partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
    desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-3/b69dbbc2b331ff0e938e456616e314dd/transformed/desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt:/Users/<USER>/.gradle/caches/transforms-3/2bf0dcf82f4f4278a5257aec9c15fdc8/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/main/kotlin:src/release/java:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/javac/release/compileReleaseJavaWithJavac/classes:/Users/<USER>/Desktop/Toggle/toggle/build/app/tmp/kotlin-classes/release:/Users/<USER>/Desktop/Toggle/toggle/build/app/kotlinToolingMetadata:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/processReleaseResources/R.jar"
      type="MAIN"
      applicationId="com.ready.lms"
      generatedSourceFolders="/Users/<USER>/Desktop/Toggle/toggle/build/app/generated/ap_generated_sources/release/out:/Users/<USER>/Desktop/Toggle/toggle/build/app/generated/source/buildConfig/release"
      generatedResourceFolders="/Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/google-services/release:/Users/<USER>/Desktop/Toggle/toggle/build/app/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-3/b69dbbc2b331ff0e938e456616e314dd/transformed/desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt:/Users/<USER>/.gradle/caches/transforms-3/2bf0dcf82f4f4278a5257aec9c15fdc8/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
