<lint-module
    format="1"
    dir="/Users/<USER>/Desktop/Toggle/toggle/android/app"
    name=":app"
    type="APP"
    maven="android:app:"
    agpVersion="8.3.2"
    buildFolder="/Users/<USER>/Desktop/Toggle/toggle/build/app"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-35/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
