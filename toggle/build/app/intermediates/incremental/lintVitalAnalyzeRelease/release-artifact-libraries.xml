<libraries>
  <library
      name="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar"
      resolved="__local_aars__:/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/release/libs.jar:unspecified"/>
  <library
      name="com.google.firebase:firebase-analytics:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/jars/classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_downloader::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/.transforms/15c34542a1ec87181ddbcb46540d8afd/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/.transforms/15c34542a1ec87181ddbcb46540d8afd/transformed/out/jars/libs/R.jar"
      resolved="vn.hunghd.flutterdownloader:flutter_downloader:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/.transforms/15c34542a1ec87181ddbcb46540d8afd/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shared_preferences_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/.transforms/327c628a7b07f4a1ecfb0c87bc6f4222/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/.transforms/327c628a7b07f4a1ecfb0c87bc6f4222/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/.transforms/327c628a7b07f4a1ecfb0c87bc6f4222/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:webview_flutter_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/.transforms/a0505084bbd16128c41f108862477a17/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/.transforms/a0505084bbd16128c41f108862477a17/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.webviewflutter:webview_flutter_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/.transforms/a0505084bbd16128c41f108862477a17/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:app_settings::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/app_settings/.transforms/15ec8ca3b9808d99bfe0b2cac252c34d/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/app_settings/.transforms/15ec8ca3b9808d99bfe0b2cac252c34d/transformed/out/jars/libs/R.jar"
      resolved="com.spencerccf.app_settings:app_settings:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/app_settings/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/app_settings/.transforms/15ec8ca3b9808d99bfe0b2cac252c34d/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:audio_session::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/audio_session/.transforms/246cb9fa9dd55bfa3ffa939d80de29f0/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/audio_session/.transforms/246cb9fa9dd55bfa3ffa939d80de29f0/transformed/out/jars/libs/R.jar"
      resolved="com.ryanheise.audio_session:audio_session:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/audio_session/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/audio_session/.transforms/246cb9fa9dd55bfa3ffa939d80de29f0/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:connectivity_plus::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/.transforms/c7157bf3a1222ba78871a62f4f9c8d5c/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/.transforms/c7157bf3a1222ba78871a62f4f9c8d5c/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.connectivity:connectivity_plus:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/.transforms/c7157bf3a1222ba78871a62f4f9c8d5c/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:device_info_plus::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/.transforms/c0c40c4beb7ff168c6ca7edd1ea3b3f1/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/.transforms/c0c40c4beb7ff168c6ca7edd1ea3b3f1/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.device_info:device_info_plus:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/.transforms/c0c40c4beb7ff168c6ca7edd1ea3b3f1/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_messaging::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/.transforms/40e8646dbf1460c023ec4f3ee44f7c32/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/.transforms/40e8646dbf1460c023ec4f3ee44f7c32/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.firebasemessaging:firebase_messaging:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/.transforms/40e8646dbf1460c023ec4f3ee44f7c32/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_core::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/.transforms/402f5f75c6fbbfee861e7c383632b739/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/.transforms/402f5f75c6fbbfee861e7c383632b739/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.firebase.core:firebase_core:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/.transforms/402f5f75c6fbbfee861e7c383632b739/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_local_notifications::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/.transforms/1dcb3fd52b9dbe1c535e5a8ab9f5c546/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/.transforms/1dcb3fd52b9dbe1c535e5a8ab9f5c546/transformed/out/jars/libs/R.jar"
      resolved="com.dexterous.flutterlocalnotifications:flutter_local_notifications:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/.transforms/1dcb3fd52b9dbe1c535e5a8ab9f5c546/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_pdfview::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/.transforms/a55993bc4381a80ca2ab1f6dc09b4add/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/.transforms/a55993bc4381a80ca2ab1f6dc09b4add/transformed/out/jars/libs/R.jar"
      resolved="io.endigo.plugings.pdfview:flutter_pdfview:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/.transforms/a55993bc4381a80ca2ab1f6dc09b4add/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/.transforms/6898fca615fed0cabf0c19849db83eb6/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/.transforms/6898fca615fed0cabf0c19849db83eb6/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/.transforms/6898fca615fed0cabf0c19849db83eb6/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:image_picker_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/.transforms/829f248c7a34e838740cfb066b27e865/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/.transforms/829f248c7a34e838740cfb066b27e865/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.imagepicker:image_picker_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/.transforms/829f248c7a34e838740cfb066b27e865/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:just_audio::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/just_audio/.transforms/5563efd2c11056dd8fab9f4764b3fc76/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/just_audio/.transforms/5563efd2c11056dd8fab9f4764b3fc76/transformed/out/jars/libs/R.jar"
      resolved="com.ryanheise.just_audio:just_audio:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/just_audio/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/just_audio/.transforms/5563efd2c11056dd8fab9f4764b3fc76/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:package_info_plus::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/.transforms/bc4b2840cbbbc0cd83dc39482e5fab0a/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/.transforms/bc4b2840cbbbc0cd83dc39482e5fab0a/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.packageinfo:package_info_plus:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/.transforms/bc4b2840cbbbc0cd83dc39482e5fab0a/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:path_provider_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/.transforms/fa8730ceb47714c637e90bdc9b7d42f1/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/.transforms/fa8730ceb47714c637e90bdc9b7d42f1/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/.transforms/fa8730ceb47714c637e90bdc9b7d42f1/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:permission_handler_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/.transforms/2091527195e16385a4974dc4798f7a86/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/.transforms/2091527195e16385a4974dc4798f7a86/transformed/out/jars/libs/R.jar"
      resolved="com.baseflow.permissionhandler:permission_handler_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/.transforms/2091527195e16385a4974dc4798f7a86/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:share_plus::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/share_plus/.transforms/c2ddfd501f0657012b9edc1c09c4b9fa/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/share_plus/.transforms/c2ddfd501f0657012b9edc1c09c4b9fa/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.share:share_plus:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/share_plus/.transforms/c2ddfd501f0657012b9edc1c09c4b9fa/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:sqflite::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/sqflite/.transforms/5cf96eca86f3dcf2a90e31c02f63c87c/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/sqflite/.transforms/5cf96eca86f3dcf2a90e31c02f63c87c/transformed/out/jars/libs/R.jar"
      resolved="com.tekartik.sqflite:sqflite:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/sqflite/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/sqflite/.transforms/5cf96eca86f3dcf2a90e31c02f63c87c/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:url_launcher_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/.transforms/bb0f5abf7ff5e38a8f21632e7b5e74ee/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/.transforms/bb0f5abf7ff5e38a8f21632e7b5e74ee/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.urllauncher:url_launcher_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/.transforms/bb0f5abf7ff5e38a8f21632e7b5e74ee/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:video_player_android::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/.transforms/88de49831b5d755e31fc33e604c73e3a/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/.transforms/88de49831b5d755e31fc33e604c73e3a/transformed/out/jars/libs/R.jar"
      resolved="io.flutter.plugins.videoplayer:video_player_android:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/.transforms/88de49831b5d755e31fc33e604c73e3a/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:wakelock_plus::release"
      jars="/Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/.transforms/52c1984d01ffbd6dd9a52bb817f6535c/transformed/out/jars/classes.jar:/Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/.transforms/52c1984d01ffbd6dd9a52bb817f6535c/transformed/out/jars/libs/R.jar"
      resolved="dev.fluttercommunity.plus.wakelock:wakelock_plus:unspecified"
      partialResultsDir="/Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/.transforms/52c1984d01ffbd6dd9a52bb817f6535c/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/flutter_embedding_release/1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7/8d57ee6078955428315d9cd81432c21f112c3bd2/flutter_embedding_release-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.1.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/jars/classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f52b7aae6c1b439e47bf5b8702293e24/transformed/jetified-firebase-installations-interop-17.1.1/jars/classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f52b7aae6c1b439e47bf5b8702293e24/transformed/jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/8afb3f2e61ed28644fdf8c005eaae0ac/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/8afb3f2e61ed28644fdf8c005eaae0ac/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/2a8868d0359859850a904d1ae55b92e1/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/2a8868d0359859850a904d1ae55b92e1/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/jars/classes.jar"
      resolved="androidx.activity:activity:1.9.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/1e15b9d4e7ad1202d50b6f531cfdd016/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/1e15b9d4e7ad1202d50b6f531cfdd016/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f7cb29123d7da9791030e84c7900eaed/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f7cb29123d7da9791030e84c7900eaed/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/jars/classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.7.0/2ad14aed781c4a73ed4dbb421966d408a0a06686/lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.7.0/85334205d65cca70ed0109c3acbd29e22a2d9cb1/lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/jars/classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/jars/classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05/jars/classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="/Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/jars/classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.7.1/c2d86b569f10b7fc7e28d3f50c0eed97897d77a7/kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4/kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-play-services/1.7.1/6333bad6f256e2ca7bc2908f586be7161a41618c/kotlinx-coroutines-play-services-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.20/e2b4d1f475ae0606d063a84fce4dccdb45c7e12a/kotlin-stdlib-jdk8-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.2.0/34dbc21d203cc4d4d623ac572a21acd4ccd716af/collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/775922ed323327ce5f94ece0a414905e/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/775922ed323327ce5f94ece0a414905e/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/3dff77bf290f834804e50da4e9b45c86/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/3dff77bf290f834804e50da4e9b45c86/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.9.1/b17951747e38bf3986a24431b9ba0d039958aa5f/annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.24/9928532f12c66ad816a625b3f9984f8368ca6d2b/kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.9.20/8b4b73f4e08efaae93fc01d8c6eaab58a72d2ab3/kotlin-stdlib-jdk7-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20"/>
  <library
      name="androidx.multidex:multidex:2.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/01b40ef94139aab404245eb9441e910a/transformed/multidex-2.0.0/jars/classes.jar"
      resolved="androidx.multidex:multidex:2.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/01b40ef94139aab404245eb9441e910a/transformed/multidex-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/arm64_v8a_release/1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7/a73c3151904ffbc50773883d2f23508efa50dcc6/arm64_v8a_release-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:guava:33.0.0-android@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/33.0.0-android/cfbbdc54f232feedb85746aeeea0722f5244bb9a/guava-33.0.0-android.jar"
      resolved="com.google.guava:guava:33.0.0-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/b421526c5f297295adef1c886e5246c39d4ac629/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.2/c4a06a64e650562f30b7bf9aaec1bfed43aca12b/failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.41.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.checkerframework/checker-qual/3.41.0/8be6df7f1e9bccb19f8f351b3651f0bac2f5e0c/checker-qual-3.41.0.jar"
      resolved="org.checkerframework:checker-qual:3.41.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.36.0/227d4d4957ccc3dc5761bd897e3a0ee587e750a7/error_prone_annotations-2.36.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.36.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-annotations/16.2.0/ba0806703ca285d03fa9c888b5868f101134a501/firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e7ba47e2b30ea6966967ac7a3702eb51/transformed/jetified-relinker-1.4.5/jars/classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e7ba47e2b30ea6966967ac7a3702eb51/transformed/jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/2.8/c85270e307e7b822f1086b93689124b89768e273/j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/jars/classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.3"
      folder="/Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/jars/classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/jars/classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/jars/classes.jar"
      resolved="androidx.datastore:datastore-preferences:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0/jars/classes.jar"
      resolved="androidx.datastore:datastore:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-preferences-core/1.0.0/403f64499b9a8994f5f7010329ddd1ee5c919ed5/datastore-preferences-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-preferences-core:1.0.0"/>
  <library
      name="androidx.datastore:datastore-core:1.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.datastore/datastore-core/1.0.0/91b04fb657294e2906d95dce6a9e5a851f6125c1/datastore-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-core:1.0.0"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.7.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/jars/classes.jar"
      resolved="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3"
      folder="/Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/497097074b60b9307e5e1daef68c080e/transformed/jetified-media3-container-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/497097074b60b9307e5e1daef68c080e/transformed/jetified-media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/dbf0b1b5c1f65c1689a20df4df20cf71/transformed/jetified-media3-decoder-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/dbf0b1b5c1f65c1689a20df4df20cf71/transformed/jetified-media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e7bc5491c559a02e4eb7de8bc96f3314/transformed/jetified-media3-database-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e7bc5491c559a02e4eb7de8bc96f3314/transformed/jetified-media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b3578cdd505b797acfce923083ec0640/transformed/jetified-media3-exoplayer-dash-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b3578cdd505b797acfce923083ec0640/transformed/jetified-media3-exoplayer-dash-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/da3273006b20e2141dad3436c0121a70/transformed/jetified-media3-exoplayer-hls-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/da3273006b20e2141dad3436c0121a70/transformed/jetified-media3-exoplayer-hls-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c1a1c21640374a44e7d23254125f664c/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c1a1c21640374a44e7d23254125f664c/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/494922f871f1e638e1150d1a1a64d7ac/transformed/jetified-media3-exoplayer-rtsp-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer-rtsp:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/494922f871f1e638e1150d1a1a64d7ac/transformed/jetified-media3-exoplayer-rtsp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/jars/classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/jars/classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.12.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/jars/classes.jar"
      resolved="androidx.webkit:webkit:1.12.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0/jars/classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/jars/classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.room/room-common/2.5.0/829a83fb92f1696a8a32f3beea884dfc87b2693/room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0/jars/classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/082442b1aa5fff4a3ae3e2e4924a8d9a/transformed/exifinterface-1.3.7/jars/classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="/Users/<USER>/.gradle/caches/transforms-3/082442b1aa5fff4a3ae3e2e4924a8d9a/transformed/exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="/Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/jars/classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="/Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-proto/16.0.0/a42d5fd83b96ae7b73a8617d29c94703e18c9992/firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/jars/classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders/17.0.0/26f52dc549c42575b155f8c720e84059ee600a85/firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e33b2d51f90bc938cb1fa605821184ed/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e33b2d51f90bc938cb1fa605821184ed/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0/jars/classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/jars/classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.12.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.12.0/10596b68aaca6230f7c40bfd9298b21ff4b84103/gson-2.12.0.jar"
      resolved="com.google.code.gson:gson:2.12.0"/>
  <library
      name="com.github.mhiew:pdfium-android:1.9.2@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/ba18c7670395478ffdea957c3ae3992b/transformed/jetified-pdfium-android-1.9.2/jars/classes.jar"
      resolved="com.github.mhiew:pdfium-android:1.9.2"
      folder="/Users/<USER>/.gradle/caches/transforms-3/ba18c7670395478ffdea957c3ae3992b/transformed/jetified-pdfium-android-1.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
