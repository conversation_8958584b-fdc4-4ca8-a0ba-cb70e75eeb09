/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=21 \
  -DANDROID_PLATFORM=android-21 \
  -DANDROID_ABI=x86_64 \
  -DCMAKE_ANDROID_ARCH_ABI=x86_64 \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264 \
  -DCMA<PERSON>_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/cxx/Debug/6ul42212/obj/x86_64 \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/cxx/Debug/6ul42212/obj/x86_64 \
  -DCMAKE_BUILD_TYPE=Debug \
  -B/Users/<USER>/Desktop/Toggle/toggle/build/.cxx/Debug/6ul42212/x86_64 \
  -GNinja \
  -Wno-dev \
  --no-warn-unused-cli
