1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ready.lms"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:5-66
15-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:4:5-80
16-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:4:22-77
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:5:5-81
17-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:5:22-78
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:6:5-81
18-->/Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:6:22-79
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->[:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
19-->[:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
20    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
20-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
20-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- See https://developer.android.com/training/package-visibility -->
21-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
21-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
22    <queries>
22-->[:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-12:15
23        <intent>
23-->[:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:18
24            <action android:name="android.intent.action.VIEW" />
24-->[:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-65
24-->[:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:21-62
25        </intent>
26    </queries>
27
28    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
28-->[:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
28-->[:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
29    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
29-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:5-82
29-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:22-79
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:25:5-79
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:25:22-76
31    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
31-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:26:5-88
31-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:26:22-85
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
32-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:27:5-82
32-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:27:22-79
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
33-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:22-78
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
34-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
34-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:22-74
35    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
35-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:26:5-110
35-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:26:22-107
36
37    <permission
37-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
38        android:name="com.ready.lms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.ready.lms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
45        android:allowBackup="true"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="true"
49        android:hardwareAccelerated="false"
50        android:icon="@mipmap/ic_launcher"
51        android:label="Toggle"
52        android:networkSecurityConfig="@xml/network_security_config"
53        android:requestLegacyExternalStorage="true"
54        android:supportsRtl="true"
55        android:usesCleartextTraffic="true" >
56        <provider
57            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
58            android:authorities="com.ready.lms.flutter_downloader.provider"
59            android:exported="false"
60            android:grantUriPermissions="true" >
61            <meta-data
61-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
62                android:name="android.support.FILE_PROVIDER_PATHS"
62-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
63                android:resource="@xml/provider_paths" />
63-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
64        </provider>
65
66        <activity
67            android:name="com.example.cepron.MainActivity"
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
69            android:exported="true"
70            android:hardwareAccelerated="true"
71            android:launchMode="singleTop"
72            android:theme="@style/LaunchTheme"
73            android:windowSoftInputMode="adjustResize" >
74
75            <!--
76                 Specifies an Android theme to apply to this Activity as soon as
77                 the Android process has started. This theme is visible to the user
78                 while the Flutter UI initializes. After that, this theme continues
79                 to determine the Window background behind the Flutter UI.
80            -->
81            <meta-data
82                android:name="io.flutter.embedding.android.NormalTheme"
83                android:resource="@style/NormalTheme" />
84
85            <intent-filter>
86                <action android:name="android.intent.action.MAIN" />
87
88                <category android:name="android.intent.category.LAUNCHER" />
89            </intent-filter>
90
91            <meta-data
92                android:name="com.google.firebase.messaging.default_notification_channel_id"
93                android:value="hight_importance_channel" />
94        </activity>
95        <!--
96             Don't delete the meta-data below.
97             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
98        -->
99        <meta-data
100            android:name="flutterEmbedding"
101            android:value="2" />
102
103        <service
103-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
104            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
104-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
105            android:exported="false"
105-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
106            android:permission="android.permission.BIND_JOB_SERVICE" />
106-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
107        <service
107-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
108            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
108-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
109            android:exported="false" >
109-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
110            <intent-filter>
110-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
111                <action android:name="com.google.firebase.MESSAGING_EVENT" />
111-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
111-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
112            </intent-filter>
113        </service>
114
115        <receiver
115-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
116            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
116-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
117            android:exported="true"
117-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
118            android:permission="com.google.android.c2dm.permission.SEND" >
118-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
119            <intent-filter>
119-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
120                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
120-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
120-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
121            </intent-filter>
122        </receiver>
123
124        <service
124-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
125            android:name="com.google.firebase.components.ComponentDiscoveryService"
125-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
126            android:directBootAware="true"
126-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
127            android:exported="false" >
127-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:56:13-37
128            <meta-data
128-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
129                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
129-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
131            <meta-data
131-->[:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
132                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
132-->[:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
134            <meta-data
134-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:57:13-59:85
135                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
135-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:58:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:59:17-82
137            <meta-data
137-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:60:13-62:85
138                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
138-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:61:17-119
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:62:17-82
140            <meta-data
140-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:37:13-39:85
141                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
141-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:38:17-139
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:39:17-82
143            <meta-data
143-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
144                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
144-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
146            <meta-data
146-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
147                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
147-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
149            <meta-data
149-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
150                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
150-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
152            <meta-data
152-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
153                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
153-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
155            <meta-data
155-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
156                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
156-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
158        </service>
159
160        <provider
160-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
161            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
161-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
162            android:authorities="com.ready.lms.flutterfirebasemessaginginitprovider"
162-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
163            android:exported="false"
163-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
164            android:initOrder="99" />
164-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
165        <provider
165-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
166            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
166-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
167            android:authorities="com.ready.lms.flutter.image_provider"
167-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
168            android:exported="false"
168-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
169            android:grantUriPermissions="true" >
169-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
170            <meta-data
170-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
171                android:name="android.support.FILE_PROVIDER_PATHS"
171-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
172                android:resource="@xml/flutter_image_picker_file_paths" />
172-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
173        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
174        <service
174-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
175            android:name="com.google.android.gms.metadata.ModuleDependencies"
175-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
176            android:enabled="false"
176-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
177            android:exported="false" >
177-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
178            <intent-filter>
178-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
179                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
179-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
179-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
180            </intent-filter>
181
182            <meta-data
182-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
183                android:name="photopicker_activity:0:required"
183-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
184                android:value="" />
184-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
185        </service>
186        <!--
187           Declares a provider which allows us to store files to share in
188           '.../caches/share_plus' and grant the receiving action access
189        -->
190        <provider
190-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
191            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
191-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
192            android:authorities="com.ready.lms.flutter.share_provider"
192-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
193            android:exported="false"
193-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
194            android:grantUriPermissions="true" >
194-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
195            <meta-data
195-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
196                android:name="android.support.FILE_PROVIDER_PATHS"
196-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
197                android:resource="@xml/flutter_share_file_paths" />
197-->[:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
198        </provider>
199        <!--
200           This manifest declared broadcast receiver allows us to use an explicit
201           Intent when creating a PendingItent to be informed of the user's choice
202        -->
203        <receiver
203-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
204            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
204-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
205            android:exported="false" >
205-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
206            <intent-filter>
206-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
207                <action android:name="EXTRA_CHOSEN_COMPONENT" />
207-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
207-->[:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
208            </intent-filter>
209        </receiver>
210
211        <activity
211-->[:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
212            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
212-->[:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
213            android:exported="false"
213-->[:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
214            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
214-->[:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
215
216        <receiver
216-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:29:9-40:20
217            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
217-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:30:13-78
218            android:exported="true"
218-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:31:13-36
219            android:permission="com.google.android.c2dm.permission.SEND" >
219-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:32:13-73
220            <intent-filter>
220-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
221                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
221-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
221-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
222            </intent-filter>
223
224            <meta-data
224-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:37:13-39:40
225                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
225-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:38:17-92
226                android:value="true" />
226-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:39:17-37
227        </receiver>
228        <!--
229             FirebaseMessagingService performs security checks at runtime,
230             but set to not exported to explicitly avoid allowing another app to call it.
231        -->
232        <service
232-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:46:9-53:19
233            android:name="com.google.firebase.messaging.FirebaseMessagingService"
233-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:47:13-82
234            android:directBootAware="true"
234-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:48:13-43
235            android:exported="false" >
235-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:49:13-37
236            <intent-filter android:priority="-500" >
236-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
237                <action android:name="com.google.firebase.MESSAGING_EVENT" />
237-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
237-->[:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
238            </intent-filter>
239        </service>
240
241        <property
241-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:30:9-32:61
242            android:name="android.adservices.AD_SERVICES_CONFIG"
242-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:31:13-65
243            android:resource="@xml/ga_ad_services_config" />
243-->[com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:32:13-58
244
245        <provider
245-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:29:9-37:20
246            android:name="androidx.startup.InitializationProvider"
246-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:30:13-67
247            android:authorities="com.ready.lms.androidx-startup"
247-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:31:13-68
248            android:exported="false" >
248-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:32:13-37
249            <meta-data
249-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
250                android:name="androidx.work.WorkManagerInitializer"
250-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
251                android:value="androidx.startup" />
251-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
252            <meta-data
252-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
253                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
253-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
254                android:value="androidx.startup" />
254-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
255            <meta-data
255-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
256                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
257                android:value="androidx.startup" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
258        </provider>
259
260        <service
260-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
261            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
261-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
263            android:enabled="@bool/enable_system_alarm_service_default"
263-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
264            android:exported="false" />
264-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
265        <service
265-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
266            android:name="androidx.work.impl.background.systemjob.SystemJobService"
266-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
267            android:directBootAware="false"
267-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
268            android:enabled="@bool/enable_system_job_service_default"
268-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
269            android:exported="true"
269-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
270            android:permission="android.permission.BIND_JOB_SERVICE" />
270-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
271        <service
271-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
272            android:name="androidx.work.impl.foreground.SystemForegroundService"
272-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
274            android:enabled="@bool/enable_system_foreground_service_default"
274-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
275            android:exported="false" />
275-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
276
277        <receiver
277-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
278            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
278-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
279            android:directBootAware="false"
279-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
280            android:enabled="true"
280-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
281            android:exported="false" />
281-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
282        <receiver
282-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
283            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
283-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
284            android:directBootAware="false"
284-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
285            android:enabled="false"
285-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
286            android:exported="false" >
286-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
287            <intent-filter>
287-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
288                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
288-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
288-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
289                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
289-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
289-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
290            </intent-filter>
291        </receiver>
292        <receiver
292-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
293            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
293-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
295            android:enabled="false"
295-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
296            android:exported="false" >
296-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
297            <intent-filter>
297-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
298                <action android:name="android.intent.action.BATTERY_OKAY" />
298-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
298-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
299                <action android:name="android.intent.action.BATTERY_LOW" />
299-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
299-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
300            </intent-filter>
301        </receiver>
302        <receiver
302-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
303            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
303-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
304            android:directBootAware="false"
304-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
305            android:enabled="false"
305-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
306            android:exported="false" >
306-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
307            <intent-filter>
307-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
308                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
308-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
308-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
309                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
309-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
309-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
310            </intent-filter>
311        </receiver>
312        <receiver
312-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
313            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
313-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
314            android:directBootAware="false"
314-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
315            android:enabled="false"
315-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
316            android:exported="false" >
316-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
317            <intent-filter>
317-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
318                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
318-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
318-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
319            </intent-filter>
320        </receiver>
321        <receiver
321-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
322            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
322-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
324            android:enabled="false"
324-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
325            android:exported="false" >
325-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
326            <intent-filter>
326-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
327                <action android:name="android.intent.action.BOOT_COMPLETED" />
327-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:17-79
327-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:25-76
328                <action android:name="android.intent.action.TIME_SET" />
328-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
328-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
329                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
329-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
329-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
330            </intent-filter>
331        </receiver>
332        <receiver
332-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
333            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
333-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
335            android:enabled="@bool/enable_system_alarm_service_default"
335-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
336            android:exported="false" >
336-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
337            <intent-filter>
337-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
338                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
338-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
338-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
339            </intent-filter>
340        </receiver>
341        <receiver
341-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
342            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
342-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
344            android:enabled="true"
344-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
345            android:exported="true"
345-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
346            android:permission="android.permission.DUMP" >
346-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
348                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
348-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
348-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
349            </intent-filter>
350        </receiver>
351
352        <provider
352-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
353            android:name="com.google.firebase.provider.FirebaseInitProvider"
353-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
354            android:authorities="com.ready.lms.firebaseinitprovider"
354-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
355            android:directBootAware="true"
355-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
356            android:exported="false"
356-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
357            android:initOrder="100" />
357-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
358
359        <receiver
359-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:29:9-33:20
360            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
360-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:30:13-85
361            android:enabled="true"
361-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:31:13-35
362            android:exported="false" >
362-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:32:13-37
363        </receiver>
364
365        <service
365-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:35:9-38:40
366            android:name="com.google.android.gms.measurement.AppMeasurementService"
366-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:36:13-84
367            android:enabled="true"
367-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:37:13-35
368            android:exported="false" />
368-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:38:13-37
369        <service
369-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:39:9-43:72
370            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
370-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:40:13-87
371            android:enabled="true"
371-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:41:13-35
372            android:exported="false"
372-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:42:13-37
373            android:permission="android.permission.BIND_JOB_SERVICE" />
373-->[com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:43:13-69
374
375        <activity
375-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
376            android:name="com.google.android.gms.common.api.GoogleApiActivity"
376-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
377            android:exported="false"
377-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
378            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
378-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
379
380        <uses-library
380-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
381            android:name="androidx.window.extensions"
381-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
382            android:required="false" />
382-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
383        <uses-library
383-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
384            android:name="androidx.window.sidecar"
384-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
385            android:required="false" />
385-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
386        <uses-library
386-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
387            android:name="android.ext.adservices"
387-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
388            android:required="false" />
388-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
389
390        <meta-data
390-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
391            android:name="com.google.android.gms.version"
391-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
392            android:value="@integer/google_play_services_version" />
392-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
393
394        <service
394-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
395            android:name="androidx.room.MultiInstanceInvalidationService"
395-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
396            android:directBootAware="true"
396-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
397            android:exported="false" />
397-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
398
399        <receiver
399-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
400            android:name="androidx.profileinstaller.ProfileInstallReceiver"
400-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
401            android:directBootAware="false"
401-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
402            android:enabled="true"
402-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
403            android:exported="true"
403-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
404            android:permission="android.permission.DUMP" >
404-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
405            <intent-filter>
405-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
406                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
406-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
406-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
407            </intent-filter>
408            <intent-filter>
408-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
409                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
409-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
409-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
410            </intent-filter>
411            <intent-filter>
411-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
412                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
412-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
412-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
413            </intent-filter>
414            <intent-filter>
414-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
415                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
415-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
415-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
416            </intent-filter>
417        </receiver>
418
419        <service
419-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
420            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
420-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
421            android:exported="false" >
421-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
422            <meta-data
422-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
423                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
423-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
424                android:value="cct" />
424-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
425        </service>
426        <service
426-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
427            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
427-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
428            android:exported="false"
428-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
429            android:permission="android.permission.BIND_JOB_SERVICE" >
429-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
430        </service>
431
432        <receiver
432-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
433            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
433-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
434            android:exported="false" />
434-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
435    </application>
436
437</manifest>
