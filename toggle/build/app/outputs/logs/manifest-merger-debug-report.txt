-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:8:5-60:19
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml
provider#vn.hunghd.flutterdownloader.DownloadedFileProvider
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:18:9-26:20
	android:authorities
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:1:1-61:12
MERGED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:1:1-61:12
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:app_settings] /Users/<USER>/Desktop/Toggle/toggle/build/app_settings/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:just_audio] /Users/<USER>/Desktop/Toggle/toggle/build/just_audio/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:audio_session] /Users/<USER>/Desktop/Toggle/toggle/build/audio_session/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-16:12
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:flutter_native_splash] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_native_splash/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_pdfview] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite] /Users/<USER>/Desktop/Toggle/toggle/build/sqflite/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:video_player_android] /Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/f52b7aae6c1b439e47bf5b8702293e24/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8afb3f2e61ed28644fdf8c005eaae0ac/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2a8868d0359859850a904d1ae55b92e1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] /Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/497097074b60b9307e5e1daef68c080e/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dbf0b1b5c1f65c1689a20df4df20cf71/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e7bc5491c559a02e4eb7de8bc96f3314/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b3578cdd505b797acfce923083ec0640/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/da3273006b20e2141dad3436c0121a70/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c1a1c21640374a44e7d23254125f664c/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/494922f871f1e638e1150d1a1a64d7ac/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1e15b9d4e7ad1202d50b6f531cfdd016/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f7cb29123d7da9791030e84c7900eaed/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/082442b1aa5fff4a3ae3e2e4924a8d9a/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e33b2d51f90bc938cb1fa605821184ed/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/775922ed323327ce5f94ece0a414905e/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/3dff77bf290f834804e50da4e9b45c86/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/01b40ef94139aab404245eb9441e910a/transformed/multidex-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/e7ba47e2b30ea6966967ac7a3702eb51/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.mhiew:pdfium-android:1.9.2] /Users/<USER>/.gradle/caches/transforms-3/ba18c7670395478ffdea957c3ae3992b/transformed/jetified-pdfium-android-1.9.2/AndroidManifest.xml:2:1-9:12
	package
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:2:5-33
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:5-66
MERGED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:5-66
MERGED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:5-66
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:3:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:4:5-80
	android:name
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:4:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:5:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:5:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:6:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/main/AndroidManifest.xml:6:22-79
uses-sdk
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
MERGED from [:app_settings] /Users/<USER>/Desktop/Toggle/toggle/build/app_settings/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:app_settings] /Users/<USER>/Desktop/Toggle/toggle/build/app_settings/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:just_audio] /Users/<USER>/Desktop/Toggle/toggle/build/just_audio/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:just_audio] /Users/<USER>/Desktop/Toggle/toggle/build/just_audio/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:audio_session] /Users/<USER>/Desktop/Toggle/toggle/build/audio_session/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:audio_session] /Users/<USER>/Desktop/Toggle/toggle/build/audio_session/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_native_splash/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_native_splash/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_pdfview] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_pdfview] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/Toggle/toggle/build/wakelock_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Desktop/Toggle/toggle/build/package_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Toggle/toggle/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/Toggle/toggle/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Toggle/toggle/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/Desktop/Toggle/toggle/build/sqflite/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/Desktop/Toggle/toggle/build/sqflite/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Desktop/Toggle/toggle/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/Toggle/toggle/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5f59316a0ed0fc52b789ee6ce6e8f6f1/transformed/jetified-firebase-analytics-22.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/9f6bd3796e11af0968e80b9fe2046cb0/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/02796edf56df3aa835f8d3f0b33bfb9c/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/fcb225d60e1af8d854128bb6f9a866f0/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/f52b7aae6c1b439e47bf5b8702293e24/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/f52b7aae6c1b439e47bf5b8702293e24/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0df207ae99c96f4b13e979d566247c1/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/c496d7f2be305ebd517cd2c2e64a8a89/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/7ac4349e02ff8a89f0c25198bd98b38e/transformed/jetified-activity-1.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/edea2fd617e432fb4d01f552d86e4c82/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/7b2e40ad2b2f39022c26dc4903372e0c/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8afb3f2e61ed28644fdf8c005eaae0ac/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8afb3f2e61ed28644fdf8c005eaae0ac/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2a8868d0359859850a904d1ae55b92e1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2a8868d0359859850a904d1ae55b92e1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/be82014be778372d1946e5b3983936ef/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/8cdab67916166815a12799dd7b562b8c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/190a932cae31875b6e7bf8091224d89b/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/c48105b13687454882850a86c4c33df5/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] /Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] /Users/<USER>/.gradle/caches/transforms-3/61330b4cde25f71c9a67925fcd9c39b2/transformed/jetified-android-pdf-viewer-3.2.0-beta.3/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/497097074b60b9307e5e1daef68c080e/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/497097074b60b9307e5e1daef68c080e/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dbf0b1b5c1f65c1689a20df4df20cf71/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dbf0b1b5c1f65c1689a20df4df20cf71/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e7bc5491c559a02e4eb7de8bc96f3314/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e7bc5491c559a02e4eb7de8bc96f3314/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b3578cdd505b797acfce923083ec0640/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b3578cdd505b797acfce923083ec0640/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/da3273006b20e2141dad3436c0121a70/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/da3273006b20e2141dad3436c0121a70/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c1a1c21640374a44e7d23254125f664c/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c1a1c21640374a44e7d23254125f664c/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/494922f871f1e638e1150d1a1a64d7ac/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/494922f871f1e638e1150d1a1a64d7ac/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/25f785092ada2b21bc0b5bc09467295a/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1e15b9d4e7ad1202d50b6f531cfdd016/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1e15b9d4e7ad1202d50b6f531cfdd016/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f7cb29123d7da9791030e84c7900eaed/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f7cb29123d7da9791030e84c7900eaed/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/dedb4fa01d529bd4c080f71992e51fe4/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/34563e424904b4dc2af1b948b701a0d3/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a7b4b094dccdd4e9710bc4850e31f16e/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/082442b1aa5fff4a3ae3e2e4924a8d9a/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/082442b1aa5fff4a3ae3e2e4924a8d9a/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/0f22b24ac821bf3d136ae202247cad0b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e33b2d51f90bc938cb1fa605821184ed/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e33b2d51f90bc938cb1fa605821184ed/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/2882cf51fb7cc859dc8ec747df215cb5/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/7f1f24c6035040e91cafd4ce27e534f3/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/775922ed323327ce5f94ece0a414905e/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/775922ed323327ce5f94ece0a414905e/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/3dff77bf290f834804e50da4e9b45c86/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/3dff77bf290f834804e50da4e9b45c86/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1518e68e72afd831e1d410a673749252/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/01b40ef94139aab404245eb9441e910a/transformed/multidex-2.0.0/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/01b40ef94139aab404245eb9441e910a/transformed/multidex-2.0.0/AndroidManifest.xml:20:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/e7ba47e2b30ea6966967ac7a3702eb51/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/e7ba47e2b30ea6966967ac7a3702eb51/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.github.mhiew:pdfium-android:1.9.2] /Users/<USER>/.gradle/caches/transforms-3/ba18c7670395478ffdea957c3ae3992b/transformed/jetified-pdfium-android-1.9.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.mhiew:pdfium-android:1.9.2] /Users/<USER>/.gradle/caches/transforms-3/ba18c7670395478ffdea957c3ae3992b/transformed/jetified-pdfium-android-1.9.2/AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/Toggle/toggle/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:connectivity_plus] /Users/<USER>/Desktop/Toggle/toggle/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
MERGED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:5-77
MERGED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:5-77
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Desktop/Toggle/toggle/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
queries
ADDED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-12:15
intent#action:name:android.intent.action.VIEW
ADDED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:18
action#android.intent.action.VIEW
ADDED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-65
	android:name
		ADDED from [:flutter_downloader] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_downloader/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:21-62
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] /Users/<USER>/Desktop/Toggle/toggle/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/Toggle/toggle/build/image_picker_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Toggle/toggle/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/Toggle/toggle/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/d9e48f0f56ad7391f30db4e9d5748720/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/304cddca41f676ae0ab2fce1ecd196ff/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:61:17-119
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6eb959db1a888f832faa0cc2efb1546/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bef95384672fc85f027d90eb95a84f5d/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/db8a2f97671bcb3c85d1d8ba839055c6/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/7772b9737ce88d933be4de2c3297ec78/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.ready.lms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.ready.lms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/3eec39df0b6d02fe76aa0cd202bd0b62/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/4c046e849a234b73d089b5ea3626448f/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/11bd080576d75c87fb6f8d2b434e5ab1/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
