# The proguard configuration file for the following section is /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.3.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.3.2
# The proguard configuration file for the following section is /Users/<USER>/development/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from /Users/<USER>/development/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro
# The proguard configuration file for the following section is /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/aapt_proguard_file/release/processReleaseResources/aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.example.cepron.MainActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class dev.fluttercommunity.plus.share.ShareFileProvider { <init>(); }
-keep class dev.fluttercommunity.plus.share.SharePlusPendingIntent { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class vn.hunghd.flutterdownloader.DownloadedFileProvider { <init>(); }
-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from /Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/aapt_proguard_file/release/processReleaseResources/aapt_rules.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/40c3205c942f0f1d27047f81d20cc2a6/transformed/jetified-play-services-measurement-api-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f41f28bf1ffdd41ce4d239be93d2f8de/transformed/work-runtime-2.9.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/consumer_proguard_dir/release/exportReleaseConsumerProguardFiles/lib0/proguard.txt
-keep class com.shockwave.**
# End of content from /Users/<USER>/Desktop/Toggle/toggle/build/flutter_pdfview/intermediates/consumer_proguard_dir/release/exportReleaseConsumerProguardFiles/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/consumer_proguard_dir/release/exportReleaseConsumerProguardFiles/lib0/proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from /Users/<USER>/Desktop/Toggle/toggle/build/flutter_plugin_android_lifecycle/intermediates/consumer_proguard_dir/release/exportReleaseConsumerProguardFiles/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/118d4d7e405adb55af1f3c3df09952e1/transformed/media-1.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/d61d8aeca485d76064e94a08b2aa428e/transformed/jetified-firebase-common-21.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f9e9425ae92bff2fe037894208f4ac8b/transformed/jetified-play-services-measurement-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/5d998cb46674cb827867806883b5b3b4/transformed/jetified-play-services-measurement-sdk-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/711572900aebffb9f92ba52d55f26306/transformed/jetified-play-services-measurement-impl-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/7e5c975cec8d65b4b8b3b970c6a3bdd2/transformed/jetified-play-services-measurement-sdk-api-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/7e57d44737bacf9d2f1d8ac1fcfd29ee/transformed/jetified-play-services-measurement-base-22.1.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/f46992e6da431563c745b557864aff4e/transformed/jetified-play-services-base-18.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/proguard.txt
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/a0dead36cbacdaef606e265e7995442b/transformed/jetified-datastore-preferences-1.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/48cb919c6ee777e8e98581f96c5faca6/transformed/jetified-window-1.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/01c927e2e0df2a97370d8352de7fd4a7/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/03cc47640813812194ec4f1768c8c522/transformed/lifecycle-viewmodel-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/dc70a6c7402da6f740078e7e75f3044b/transformed/rules/lib/META-INF/com.android.tools/r8/coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from /Users/<USER>/.gradle/caches/transforms-3/dc70a6c7402da6f740078e7e75f3044b/transformed/rules/lib/META-INF/com.android.tools/r8/coroutines.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/proguard.txt


# End of content from /Users/<USER>/.gradle/caches/transforms-3/ecab2546a89286216d047fe54352de1b/transformed/jetified-play-services-tasks-18.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/704d4a63fcc1dfaad752ac96c0b8c12e/transformed/jetified-play-services-basement-18.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f703537003239040f34e793a464c6fb6/transformed/fragment-1.7.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote androidx.media3.decoder.flac.FlacExtractor
-keepclassmembers class androidx.media3.decoder.flac.FlacExtractor {
  <init>(int);
}
-dontnote androidx.media3.decoder.flac.FlacLibrary
-keepclassmembers class androidx.media3.decoder.flac.FlacLibrary {
  public static boolean isAvailable();
}
-dontnote androidx.media3.decoder.midi.MidiExtractor
-keepclassmembers class androidx.media3.decoder.midi.MidiExtractor {
  <init>();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/a7b069381550fb4caad1eb8f2db8fbe8/transformed/jetified-media3-extractor-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class androidx.media3.datasource.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote androidx.media3.datasource.rtmp.RtmpDataSource
-keepclassmembers class androidx.media3.datasource.rtmp.RtmpDataSource {
  <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/ac9544cd6f9c7dcd847da0e6da8cd913/transformed/jetified-media3-datasource-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from /Users/<USER>/.gradle/caches/transforms-3/4710723fd6a246c4104cf43309d6fcbf/transformed/jetified-media3-common-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote androidx.media3.decoder.vp9.LibvpxVideoRenderer
-keepclassmembers class androidx.media3.decoder.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.av1.Libgav1VideoRenderer
-keepclassmembers class androidx.media3.decoder.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.opus.LibopusAudioRenderer
-keepclassmembers class androidx.media3.decoder.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.flac.LibflacAudioRenderer
-keepclassmembers class androidx.media3.decoder.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.midi.MidiRenderer
-keepclassmembers class androidx.media3.decoder.midi.MidiRenderer {
  <init>(android.content.Context);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote androidx.media3.exoplayer.dash.offline.DashDownloader
-keepclassmembers class androidx.media3.exoplayer.dash.offline.DashDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.hls.offline.HlsDownloader
-keepclassmembers class androidx.media3.exoplayer.hls.offline.HlsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader
-keepclassmembers class androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote androidx.media3.exoplayer.dash.DashMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.dash.DashMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.hls.HlsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.hls.HlsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory {
  <init>();
}

# Constructors and methods accessed via reflection in CompositingVideoSinkProvider
-dontnote androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory
-keepclasseswithmembers class androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory {
  <init>(androidx.media3.common.VideoFrameProcessor$Factory);
}
-dontnote androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder
-keepclasseswithmembers class androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder {
  androidx.media3.effect.DefaultVideoFrameProcessor$Factory build();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/97c218c4cb60d0407e4a869ad3e05147/transformed/jetified-media3-exoplayer-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/385343779efa4df410cdce855d3a1714/transformed/webkit-1.12.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/311adcef505c6524cc7e8335e797703f/transformed/core-1.13.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/a24af67863b0006ee132781e493f7f17/transformed/lifecycle-runtime-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/5a8924de3be45af361ea864f6ca72267/transformed/jetified-lifecycle-process-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/819ffa9c8531344ffe1417e96d67122f/transformed/jetified-savedstate-1.2.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/42828b46c8080b552fc82ee27c9b1ce7/transformed/rules/lib/META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/42828b46c8080b552fc82ee27c9b1ce7/transformed/rules/lib/META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e49e9d9a1a05263d6526d1ce45d06ce8/transformed/room-runtime-2.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from /Users/<USER>/.gradle/caches/transforms-3/bf3e9b4d436cba2141326ab0c8f242a4/transformed/jetified-startup-runtime-1.1.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e358c880556776aed11c24b71cdd46a2/transformed/jetified-firebase-components-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f876eb5261099c96e77d313067355641/transformed/jetified-transport-backend-cct-3.1.9/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/proguard.txt

# End of content from /Users/<USER>/.gradle/caches/transforms-3/312309d45bbeb6e4f1c4e2ac565c9f16/transformed/jetified-firebase-encoders-json-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/3e7606b84de789134d2f524d025c23d0/transformed/jetified-transport-api-3.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f27952f2f43b9b68e8e91e2b7873fadd/transformed/versionedparcelable-1.1.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/86732f72f4c7c28d45b5558ebc2657a5/transformed/jetified-core-1.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c4651078bc9eeb802620006e55d07808/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/c4651078bc9eeb802620006e55d07808/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/edd3377e2f2d6b63a50120c8c1715118/transformed/rules/lib/META-INF/proguard/gson.pro
### Gson ProGuard and R8 rules which are relevant for all users
### This file is automatically recognized by ProGuard and R8, see https://developer.android.com/build/shrink-code#configuration-files
###
### IMPORTANT:
### - These rules are additive; don't include anything here which is not specific to Gson (such as completely
###   disabling obfuscation for all classes); the user would be unable to disable that then
### - These rules are not complete; users will most likely have to add additional rules for their specific
###   classes, for example to disable obfuscation for certain fields or to keep no-args constructors
###

# Keep generic signatures; needed for correct type resolution
-keepattributes Signature

# Keep Gson annotations
# Note: Cannot perform finer selection here to only cover Gson annotations, see also https://stackoverflow.com/q/47515093
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

### The following rules are needed for R8 in "full mode" which only adheres to `-keepattribtues` if
### the corresponding class or field is matches by a `-keep` rule as well, see
### https://r8.googlesource.com/r8/+/refs/heads/main/compatibility-faq.md#r8-full-mode

# Keep class TypeToken (respectively its generic signature) if present
-if class com.google.gson.reflect.TypeToken
-keep,allowobfuscation class com.google.gson.reflect.TypeToken

# Keep any (anonymous) classes extending TypeToken
-keep,allowobfuscation class * extends com.google.gson.reflect.TypeToken

# Keep classes with @JsonAdapter annotation
-keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *

# Keep fields with any other Gson annotation
# Also allow obfuscation, assuming that users will additionally use @SerializedName or
# other means to preserve the field names
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.Expose <fields>;
  @com.google.gson.annotations.JsonAdapter <fields>;
  @com.google.gson.annotations.Since <fields>;
  @com.google.gson.annotations.Until <fields>;
}

# Keep no-args constructor of classes which can be used with @JsonAdapter
# By default their no-args constructor is invoked to create an adapter instance
-keepclassmembers class * extends com.google.gson.TypeAdapter {
  <init>();
}
-keepclassmembers class * implements com.google.gson.TypeAdapterFactory {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonSerializer {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonDeserializer {
  <init>();
}

# Keep fields annotated with @SerializedName for classes which are referenced.
# If classes with fields annotated with @SerializedName have a no-args
# constructor keep that as well. Based on
# https://issuetracker.google.com/issues/150189783#comment11.
# See also https://github.com/google/gson/pull/2420#discussion_r1241813541
# for a more detailed explanation.
-if class *
-keepclasseswithmembers,allowobfuscation class <1> {
  @com.google.gson.annotations.SerializedName <fields>;
}
-if class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclassmembers,allowobfuscation,allowoptimization class <1> {
  <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/edd3377e2f2d6b63a50120c8c1715118/transformed/rules/lib/META-INF/proguard/gson.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>