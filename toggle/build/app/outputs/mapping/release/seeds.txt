com.google.android.gms.measurement.AppMeasurementReceiver
com.google.android.gms.measurement.AppMeasurement
com.google.android.gms.measurement.AppMeasurementService
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface
androidx.work.WorkerParameters
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior
androidx.media.AudioAttributesImplBase
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin
androidx.fragment.app.DialogFragment
io.flutter.plugins.pathprovider.PathProviderPlugin
com.ryanheise.audio_session.AudioSessionPlugin
com.ryanheise.just_audio.JustAudioPlugin
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin
io.flutter.plugins.urllauncher.UrlLauncherPlugin
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface
androidx.startup.InitializationProvider
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
com.google.android.gms.common.SupportErrorDialogFragment
dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
io.flutter.plugins.firebase.core.FlutterFirebasePlugin
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.media.AudioAttributesImplApi21Parcelizer
com.dexterous.flutterlocalnotifications.models.NotificationStyle
androidx.core.graphics.drawable.IconCompat
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor
com.shockwave.pdfium.PdfDocument$Meta
com.google.firebase.iid.FirebaseInstanceIdReceiver
androidx.work.impl.WorkDatabase
com.google.android.gms.common.api.internal.zzd
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
com.dexterous.flutterlocalnotifications.models.ScheduleMode
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty
android.support.v4.app.RemoteActionCompatParcelizer
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory
org.chromium.support_lib_boundary.StaticsBoundaryInterface
android.support.v4.media.session.MediaSessionCompat$QueueItem
com.google.firebase.datatransport.TransportRegistrar
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
android.support.v4.media.MediaBrowserCompat$SearchResultReceiver
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
com.dexterous.flutterlocalnotifications.models.NotificationAction
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface
dev.fluttercommunity.plus.share.SharePlusPendingIntent
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
androidx.work.impl.background.systemalarm.RescheduleReceiver
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
dev.fluttercommunity.plus.share.SharePlusPlugin
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
androidx.window.extensions.core.util.function.Predicate
com.google.firebase.messaging.FirebaseMessagingRegistrar
com.google.android.gms.common.api.Scope
io.flutter.view.TextureRegistry$GLTextureConsumer
androidx.work.impl.background.systemjob.SystemJobService
android.support.v4.media.AudioAttributesCompatParcelizer
androidx.core.app.CoreComponentFactory
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingPlugin
vn.hunghd.flutterdownloader.DownloadWorker
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin
vn.hunghd.flutterdownloader.DownloadedFileProvider
android.support.v4.media.RatingCompat
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.work.impl.workers.DiagnosticsWorker
androidx.core.app.RemoteActionCompat
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
com.dexterous.flutterlocalnotifications.models.SoundSource
com.google.android.gms.common.api.Status
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$1
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface
io.flutter.view.FlutterCallbackInformation
android.support.v4.media.session.PlaybackStateCompat$CustomAction
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar
kotlinx.coroutines.android.AndroidDispatcherFactory
io.flutter.view.TextureRegistry$SurfaceTextureEntry
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
com.dexterous.flutterlocalnotifications.models.styles.StyleInformation
androidx.work.impl.workers.ConstraintTrackingWorker
io.flutter.plugins.imagepicker.ImagePickerFileProvider
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.work.Worker
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin
androidx.media3.exoplayer.dash.DashMediaSource$Factory
android.support.v4.media.session.MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
androidx.window.extensions.core.util.function.Consumer
com.google.android.gms.measurement.AppMeasurementJobService
io.flutter.embedding.engine.FlutterOverlaySurface
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
androidx.core.app.RemoteActionCompatParcelizer
com.google.android.gms.common.annotation.KeepName
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver
com.google.firebase.analytics.FirebaseAnalytics
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
androidx.window.extensions.core.util.function.Function
com.dexterous.flutterlocalnotifications.models.RepeatInterval
androidx.versionedparcelable.CustomVersionedParcelable
com.google.android.gms.common.api.internal.LifecycleCallback
androidx.work.impl.background.systemalarm.SystemAlarmService
com.baseflow.permissionhandler.PermissionHandlerPlugin
com.google.android.gms.common.util.DynamiteApi
dev.fluttercommunity.plus.share.ShareFileProvider
androidx.work.OverwritingInputMerger
androidx.media3.exoplayer.hls.HlsMediaSource$Factory
androidx.work.impl.diagnostics.DiagnosticsReceiver
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface
com.google.firebase.components.ComponentRegistrar
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.work.impl.workers.CombineContinuationsWorker
com.shockwave.pdfium.PdfDocument
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
android.support.v4.media.AudioAttributesImplApi26Parcelizer
androidx.media.AudioAttributesCompatParcelizer
com.shockwave.pdfium.PdfDocument$Bookmark
io.endigo.plugins.pdfviewflutter.PDFViewFlutterPlugin
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation
com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface
com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar
androidx.profileinstaller.ProfileInstallerInitializer
androidx.work.impl.WorkDatabase_Impl
android.support.v4.media.session.PlaybackStateCompat
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants
io.flutter.plugin.text.ProcessTextPlugin
com.google.firebase.messaging.FirebaseMessaging
android.support.v4.media.session.ParcelableVolumeInfo
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
io.flutter.plugins.urllauncher.WebViewActivity
androidx.lifecycle.DefaultLifecycleObserver
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface
androidx.profileinstaller.ProfileInstallReceiver
android.support.v4.media.session.MediaSessionCompat$Token
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation
androidx.media.AudioAttributesImplApi26
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
com.dexterous.flutterlocalnotifications.utils.BooleanUtils
io.flutter.plugins.imagepicker.ImagePickerPlugin
androidx.media.AudioAttributesImplApi26Parcelizer
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus
com.shockwave.pdfium.R
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
com.google.firebase.messaging.FirebaseMessagingService
com.example.cepron.MainActivity
androidx.work.impl.foreground.SystemForegroundService
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation
androidx.lifecycle.ProcessLifecycleInitializer
com.google.android.datatransport.cct.CctBackendFactory
com.google.android.gms.common.internal.ReflectedParcelable
com.google.android.gms.common.api.internal.BasePendingResult
com.dexterous.flutterlocalnotifications.models.BitmapSource
com.google.firebase.installations.FirebaseInstallationsRegistrar
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
com.shockwave.pdfium.util.SizeF
androidx.media.AudioAttributesImpl
io.flutter.plugin.platform.SingleViewPresentation
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface
android.support.v4.media.MediaBrowserCompat$ItemReceiver
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
com.shockwave.pdfium.PdfDocument$Link
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
com.google.firebase.ktx.FirebaseCommonKtxRegistrar
com.google.android.gms.auth.api.signin.GoogleSignInAccount
androidx.work.CoroutineWorker
com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.browser.browseractions.BrowserActionsFallbackMenuView
com.google.firebase.provider.FirebaseInitProvider
io.flutter.view.AccessibilityViewEmbedder
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface
com.spencerccf.app_settings.AppSettingsPlugin
androidx.webkit.WebViewClientCompat
com.dexterous.flutterlocalnotifications.models.IconSource
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry
io.flutter.view.TextureRegistry$SurfaceProducer
org.chromium.support_lib_boundary.PrefetchOperationResultBoundaryInterface
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService
com.dexterous.flutterlocalnotifications.utils.StringUtils
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
org.chromium.support_lib_boundary.WebMessageBoundaryInterface
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException
androidx.annotation.Keep
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin
com.google.firebase.FirebaseCommonRegistrar
androidx.work.WorkManagerInitializer
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface
com.tekartik.sqflite.SqflitePlugin
io.flutter.plugins.videoplayer.VideoPlayerPlugin
com.google.android.gms.common.api.GoogleApiActivity
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver$1
com.shockwave.pdfium.PdfPasswordException
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.versionedparcelable.ParcelImpl
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails
com.shockwave.pdfium.BuildConfig
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus
androidx.media.AudioAttributesImplApi21
io.flutter.view.TextureRegistry$ImageConsumer
android.support.v4.graphics.drawable.IconCompatParcelizer
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface
com.google.firebase.FirebaseCommonKtxRegistrar
com.google.android.gms.common.GooglePlayServicesIncorrectManifestValueException
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface
com.google.android.gms.common.GooglePlayServicesManifestException
com.google.firebase.concurrent.ExecutorsRegistrar
com.dexterous.flutterlocalnotifications.models.NotificationDetails
io.flutter.embedding.engine.FlutterJNI
android.support.v4.media.AudioAttributesImplBaseParcelizer
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface
com.google.firebase.components.ComponentDiscoveryService
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface
android.support.v4.media.AudioAttributesImplApi21Parcelizer
android.support.v4.media.MediaBrowserCompat$MediaItem
kotlin.coroutines.jvm.internal.BaseContinuationImpl
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin
android.support.v4.media.MediaMetadataCompat
org.chromium.support_lib_boundary.ProfileBoundaryInterface
android.support.v4.media.MediaDescriptionCompat
com.dexterous.flutterlocalnotifications.models.DateTimeComponents
com.dexterous.flutterlocalnotifications.models.PersonDetails
io.flutter.plugins.GeneratedPluginRegistrant
com.shockwave.pdfium.util.Size
androidx.media.AudioAttributesImplBaseParcelizer
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
com.shockwave.pdfium.PdfiumCore
io.flutter.view.TextureRegistry$ImageTextureEntry
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation
android.support.v4.media.MediaBrowserCompat$CustomActionResultReceiver
androidx.work.ArrayCreatingInputMerger
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface
com.dexterous.flutterlocalnotifications.models.MessageDetails
com.dexterous.flutterlocalnotifications.models.Time
androidx.media.AudioAttributesCompat
com.google.gson.reflect.TypeToken
androidx.room.MultiInstanceInvalidationService
vn.hunghd.flutterdownloader.FlutterDownloaderPlugin
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SHOW_METHOD
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource DrawableResource
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String title
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_GREEN
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzfy$zzm zzc
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATION_MESSAGING_STYLE_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean usesChronometer
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzh
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String NAME
com.google.android.gms.internal.measurement.zzfr$zzh: java.lang.String zzg
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzbh
androidx.media3.extractor.metadata.icy.IcyHeaders: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
com.google.android.gms.internal.measurement.zzfr$zza$zza: int zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_ACTION
androidx.media3.extractor.metadata.scte35.PrivateCommand: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayList lastDequeuedImage
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean showsUserInterface
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zzf zzs
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean enableVibration
com.google.android.gms.internal.measurement.zzfr$zzc: boolean zzh
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String GROUP_ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String category
com.google.android.gms.internal.measurement.zzfy$zzn: com.google.android.gms.internal.measurement.zzfy$zzn zzc
com.google.android.gms.internal.measurement.zzfr$zzd: java.lang.String zzq
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PERSON
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATIONS_METHOD
com.google.android.gms.internal.measurement.zzfr$zza$zzf: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource DrawableResource
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.SoundSource soundSource
com.google.android.gms.internal.measurement.zzfo$zza: int zzf
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_ALL_PENDING_NOTIFICATIONS_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer iconResourceId
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long calledAt
com.google.android.gms.internal.measurement.zzfy$zzk: int zzak
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer hour
com.google.android.gms.internal.measurement.zzgd$zzc: com.google.android.gms.internal.measurement.zzkc zzf
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
com.google.android.gms.internal.measurement.zzfy$zzi: com.google.android.gms.internal.measurement.zzfy$zzb zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelDescription
android.support.v4.media.session.MediaSessionCompat$QueueItem: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzd: java.lang.String zzp
androidx.media3.common.StreamKey: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PROGRESS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.RepeatInterval repeatInterval
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] $VALUES
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String IMPORTANCE
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzbj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_OFF_MS
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
com.google.android.gms.internal.measurement.zzfr$zza$zzf: com.google.android.gms.internal.measurement.zzll zzd
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int DISABLED
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: io.flutter.plugin.common.MethodChannel channel
com.google.android.gms.internal.measurement.zzgd$zzc: com.google.android.gms.internal.measurement.zzgd$zza zzg
com.google.android.gms.internal.measurement.zzfy$zzi: java.lang.String zzg
com.google.android.gms.internal.measurement.zzdw: android.os.Parcelable$Creator CREATOR
com.google.firebase.messaging.FirebaseMessagingRegistrar: java.lang.String LIBRARY_NAME
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int DISABLED
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode inexactAllowWhileIdle
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.android.gms.internal.measurement.zzfy$zzk: int zzbl
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_DEQUEUED_IMAGES
com.google.android.gms.internal.measurement.zzfy$zzd: com.google.android.gms.internal.measurement.zzfy$zzm zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MAX_PROGRESS
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int APP
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ACTION_ID
com.google.android.gms.internal.measurement.zzfy$zzi: com.google.android.gms.internal.measurement.zzfy$zzi zzc
com.google.android.gms.internal.measurement.zzfr$zzb: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.common.internal.zzk: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface: int FAILURE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Object largeIcon
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzkc zzag
kotlinx.coroutines.CancelledContinuation: int _resumed
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String scheduledDateTime
com.google.android.gms.internal.measurement.zzfy$zzn: com.google.android.gms.internal.measurement.zzjz zzg
com.google.android.gms.internal.measurement.zzfy$zzh: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String icon
com.google.android.gms.common.ConnectionResult: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ACTIONS
androidx.media3.extractor.metadata.scte35.TimeSignalCommand: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String USES_CHRONOMETER
com.google.android.gms.internal.measurement.zzfy$zzd: com.google.android.gms.internal.measurement.zzfy$zzd zzc
com.google.android.gms.internal.measurement.zzfo$zzb: int zze
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
androidx.media3.extractor.metadata.id3.PrivFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle BigPicture
com.google.android.gms.internal.measurement.zzfo$zzd: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfr$zzi: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CATEGORY
com.google.android.gms.internal.measurement.zzfo$zzf: java.lang.String zzg
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.String summaryText
com.google.android.gms.internal.measurement.zzfy$zze: long zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_CONVERSATION
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
com.google.android.gms.internal.measurement.zzfr$zzg: java.lang.String zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATION_MESSAGING_STYLE_ERROR_CODE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PAYLOAD
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzp
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_RAW_RESOURCE_ERROR_MESSAGE
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzkc zzk
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource FlutterBitmapAsset
com.google.android.gms.internal.measurement.zzfr$zza$zzc: com.google.android.gms.internal.measurement.zzfr$zza$zzc zzc
com.google.android.gms.internal.measurement.zzfo$zzf: com.google.android.gms.internal.measurement.zzkc zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String subText
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource Uri
androidx.fragment.app.BackStackRecordState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String SHOWS_USER_INTERFACE
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float finalOpacity
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
androidx.media3.container.Mp4LocationData: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String START_FOREGROUND_SERVICE
com.google.android.gms.internal.measurement.zzfr$zzc: boolean zzg
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
kotlinx.coroutines.DispatchedCoroutine: int _decision
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.android.gms.internal.measurement.zzfy$zzo: com.google.android.gms.internal.measurement.zzfy$zzo zzc
com.google.android.gms.internal.measurement.zzfy$zzh: com.google.android.gms.internal.measurement.zzkc zzk
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ONGOING
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String DESCRIPTION
com.google.android.gms.internal.measurement.zzfr$zza$zzc: int zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean colorized
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String URI
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MESSAGES
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String CONTEXTUAL
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String groupKey
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTriggeredEventParams
com.dexterous.flutterlocalnotifications.models.PersonDetails: com.dexterous.flutterlocalnotifications.models.IconSource iconBitmapSource
com.google.android.gms.internal.measurement.zzfy$zza: com.google.android.gms.internal.measurement.zzfy$zza zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIMESTAMP
com.google.android.gms.internal.measurement.zzfy$zzi: int zze
com.google.android.gms.internal.measurement.zzfy$zzg: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.Time repeatTime
com.google.android.gms.internal.measurement.zzfr$zzf: int zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SHARED_PREFERENCES_KEY
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.PermissionRequestListener callback
com.google.android.gms.internal.measurement.zzfy$zzo: long zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BOT
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: java.lang.String MODULE_ID
com.google.android.gms.internal.measurement.zzfr$zzb: com.google.android.gms.internal.measurement.zzkc zzg
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int BROWSER
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledOnMs
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: com.dexterous.flutterlocalnotifications.models.BitmapSource largeIconBitmapSource
com.google.android.gms.internal.measurement.zzfy$zzo: java.lang.String zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ENABLE_LIGHTS
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] $VALUES
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
com.google.android.gms.internal.measurement.zzfy$zze: com.google.android.gms.internal.measurement.zzll zzd
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int DISABLED
android.support.v4.media.session.MediaSessionCompat$Token: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_LINES
com.google.android.gms.internal.measurement.zzfy$zzk: long zzx
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean showWhen
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.media3.extractor.metadata.scte35.SpliceInsertCommand: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zza$zza: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.util.List actions
com.google.android.gms.internal.measurement.zzfr$zzi: java.lang.String zzh
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String dataUri
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzk
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzq
androidx.media3.common.Metadata: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zzg zzu
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzf
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
androidx.media3.extractor.metadata.id3.InternalFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_ALL_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean bypassDnd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_CHANNELS_ERROR_CODE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String HAS_NOTIFICATION_POLICY_ACCESS_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_BLUE
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: com.dexterous.flutterlocalnotifications.models.PersonDetails person
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggeredEventName
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_CONTENT_TITLE
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
com.google.android.gms.internal.measurement.zzfr$zza$zzc: int zze
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String NAME
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zzd zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledOffMs
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzar
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_DESCRIPTION
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_TIME
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String EXACT_ALARMS_PERMISSION_ERROR_CODE
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.String typeFieldName
androidx.media3.extractor.metadata.scte35.SpliceScheduleCommand: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String STOP_FOREGROUND_SERVICE
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: java.util.concurrent.ExecutorService cachedThreadPool
com.google.android.gms.internal.measurement.zzfy$zzi: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzz
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Weekly
com.google.android.gms.internal.measurement.zzfo$zzb: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zze: int zze
androidx.media3.extractor.metadata.scte35.SpliceNullCommand: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean showBadge
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzkc zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHRONOMETER_COUNT_DOWN
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_CONTENT
com.google.android.gms.internal.measurement.zzfr$zzd: long zzf
com.google.android.gms.internal.measurement.zzfy$zzd: boolean zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_SUMMARY_TEXT
com.google.android.gms.internal.measurement.zzfo$zze: boolean zzj
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zzf: int zzf
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer audioAttributesUsage
com.google.android.gms.internal.measurement.zzfy$zzf: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zzc: boolean zzh
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzo
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean onlyAlertOnce
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatContentTitle
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HIDE_EXPANDED_LARGE_ICON
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzl
com.google.android.gms.internal.measurement.zzfr$zzi: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: java.lang.Boolean htmlFormatBody
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String BYPASS_DND
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ID
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String TAG
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.Class baseType
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean htmlFormatSummaryText
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzg
com.google.android.gms.internal.measurement.zzfy$zzc: int zze
kotlinx.coroutines.DefaultExecutor: int debugStatus
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CONVERSATION_TITLE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DELETE_NOTIFICATION_CHANNEL_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LARGE_ICON
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long when
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String description
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mCreationTimestamp
com.google.android.gms.internal.measurement.zzgd$zzd: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfr$zzb: boolean zzh
com.google.android.gms.internal.measurement.zzfo$zzb: com.google.android.gms.internal.measurement.zzkc zzh
com.google.android.gms.internal.measurement.zzfy$zzk: int zzaj
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ONLY_ALERT_ONCE
com.google.android.gms.internal.measurement.zzfr$zzb: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ICON_SOURCE
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource FilePath
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_BIG_PICTURE_ERROR_CODE
com.google.android.gms.measurement.internal.zzo: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.Activity mainActivity
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.JobSupport: java.lang.Object _state
com.google.android.gms.internal.measurement.zzfy$zza: int zze
androidx.media3.extractor.metadata.id3.GeobFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer repeatIntervalMilliseconds
com.google.android.gms.internal.measurement.zzfr$zzh: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zzd: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean playSound
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ID
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
com.google.android.gms.internal.measurement.zzfr$zze: com.google.android.gms.internal.measurement.zzkc zze
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: long[] vibrationPattern
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelId
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTimeToLive
com.google.android.gms.internal.measurement.zzfy$zzd: int zze
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String CHANNEL_ACTION
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzg
com.google.android.gms.internal.measurement.zzfy$zzf: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzfr$zza$zza: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SOUND_SOURCE
com.google.android.gms.internal.measurement.zzfr$zzh: com.google.android.gms.internal.measurement.zzfr$zzh zzc
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean playSound
com.google.android.gms.internal.measurement.zzfy$zzb: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfr$zzf: com.google.android.gms.internal.measurement.zzfr$zzf zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String KEY
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String VISIBILITY
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String text
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mAppId
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
com.google.android.gms.internal.measurement.zzfy$zzj: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzfo$zze: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_INTERVAL_MILLISECONDS
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String UNSUPPORTED_OS_VERSION_ERROR_CODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer color
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_STRING
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DEFAULT_ICON
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String key
com.google.android.gms.internal.measurement.zzfy$zzk: int zzbo
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CALLED_AT
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.util.ArrayList lines
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_ALPHA
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode inexact
com.google.android.gms.internal.measurement.zzjt: int zzd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_RESPONSE_TYPE
android.support.v4.media.session.PlaybackStateCompat$CustomAction: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
com.google.android.gms.internal.measurement.zzfo$zzc: java.lang.String zzi
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String METHOD_CHANNEL
com.google.android.gms.internal.measurement.zzfy$zzf: long zzi
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Messaging
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ZONED_SCHEDULE_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long millisecondsSinceEpoch
com.google.android.gms.signin.internal.zak: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer groupAlertBehavior
com.google.android.gms.common.internal.zat: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String dataMimeType
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_ALERT_BEHAVIOR
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzll zzd
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String DESCRIPTION
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.google.android.gms.internal.measurement.zzfo$zzb: boolean zzm
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TITLE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_INTERVAL
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String HOUR
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbq
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ALLOW_GENERATED_REPLIES
android.support.v4.media.session.ParcelableVolumeInfo: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TICKER
com.google.android.gms.internal.measurement.zzfy$zzj: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LINES
com.google.android.gms.internal.measurement.zzfo$zzd: java.lang.String zzj
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DayOfWeekAndTime
androidx.media3.extractor.metadata.id3.ApicFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzc: int zze
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
com.google.android.gms.internal.measurement.zzfy$zzo: java.lang.String zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.styles.StyleInformation styleInformation
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbm
com.google.android.gms.internal.measurement.zzfr$zza$zzf: java.lang.String zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_NOTIFICATIONS_PERMISSION_METHOD
com.google.android.gms.signin.internal.zag: android.os.Parcelable$Creator CREATOR
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.Object mValue
com.google.android.gms.common.internal.MethodInvocation: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: java.lang.Boolean htmlFormatTitle
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
com.google.android.gms.internal.measurement.zzfy$zzk: long zzl
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LED_DETAILS_ERROR_MESSAGE
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.String contentTitle
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String groupId
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ARE_NOTIFICATIONS_ENABLED_METHOD
com.google.android.gms.internal.measurement.zzfr$zzd: boolean zzm
com.google.android.gms.internal.measurement.zzfo$zzd: java.lang.String zzh
com.google.android.gms.internal.measurement.zzfy$zzk: long zzm
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] $VALUES
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfy$zzk: long zzan
com.google.android.gms.internal.measurement.zzgd$zzd: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String VIBRATION_PATTERN
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_SUFFIX
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource ContentUri
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULE_MODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelName
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzad
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CREATE_NOTIFICATION_CHANNEL_METHOD
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
com.google.android.gms.internal.measurement.zzfy$zzg: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfy$zzh: java.lang.String zzg
com.google.android.gms.internal.measurement.zzfr$zzd: java.lang.String zzg
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean htmlFormatContentTitle
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
com.google.android.gms.internal.measurement.zzfr$zzd: java.lang.String zzl
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_ON_MS
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_RED
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String FULL_SCREEN_INTENT
com.google.android.gms.internal.measurement.zzgd$zzd: java.lang.String zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String payload
com.google.android.gms.internal.measurement.zzfo$zza: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.common.zzs: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Object largeIcon
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CALLBACK_HANDLE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DRAWABLE
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String icon
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzaz
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzkc zzg
androidx.media3.extractor.metadata.id3.TextInformationFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DateAndTime
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERMISSION_REQUEST_IN_PROGRESS_ERROR_CODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledColor
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String name
androidx.media3.extractor.metadata.mp4.SlowMotionData: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_SOUND_ERROR_CODE
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzfy$zzk zzc
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String name
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzr
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzkc zzo
androidx.media3.extractor.metadata.vorbis.VorbisComment: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: com.dexterous.flutterlocalnotifications.models.IconSource iconSource
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource ByteArray
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLORIZED
com.google.android.gms.internal.measurement.zzfy$zzh: double zzj
com.google.android.gms.internal.measurement.zzfy$zzo: long zzf
com.google.android.gms.internal.measurement.zzfr$zza: int zze
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_DRAWABLE_RESOURCE_ERROR_MESSAGE
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer second
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
com.google.android.gms.internal.measurement.zzfy$zzn: int zzf
com.google.android.gms.internal.measurement.zzfy$zzk: long zzau
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_TAG
com.google.android.gms.internal.measurement.zzfo$zzc: int zze
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_BASE_PATH
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
com.google.android.gms.internal.measurement.zzgd$zzb: com.google.android.gms.internal.measurement.zzkc zzg
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController2 platformViewsController2
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzaf
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
androidx.media3.extractor.metadata.id3.UrlLinkFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgd$zzd: boolean zzj
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Hourly
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzi
com.google.android.gms.internal.measurement.zzfy$zzn: int zze
androidx.datastore.preferences.PreferencesProto$Value: int bitField0_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean enableVibration
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_RED
com.dexterous.flutterlocalnotifications.models.NotificationDetails: int[] additionalFlags
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mOrigin
com.google.android.gms.internal.measurement.zzfy$zzc: boolean zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PLATFORM_SPECIFICS
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface: int SUCCESS
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int FULL_SCREEN_INTENT_PERMISSION_REQUEST_CODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String tag
android.support.v4.media.RatingCompat: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzc: com.google.android.gms.internal.measurement.zzfr$zzc zzc
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_APP_LAUNCH_DETAILS_METHOD
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int NONE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PRIORITY
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String uri
com.google.android.gms.internal.measurement.zzgd$zzd: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String AUTO_CANCEL
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INPUT_RESULT
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean allowGeneratedReplies
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean showProgress
com.google.android.gms.internal.measurement.zzfo$zzd: int zze
com.google.android.gms.internal.measurement.zzfy$zzd: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean $assertionsDisabled
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean hideExpandedLargeIcon
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CONTENT_TITLE
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String summaryText
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MILLISECONDS_SINCE_EPOCH
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzt
com.google.android.gms.internal.measurement.zzfo$zzd: boolean zzg
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String PLAY_SOUND
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String INDETERMINATE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
com.google.android.gms.measurement.internal.zzbe: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHORTCUT_ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_SHOW_BADGE
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzap
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULED_NOTIFICATION_REPEAT_FREQUENCY
com.google.android.gms.internal.measurement.zzfr$zzi: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SUB_TEXT
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.util.Map subtypeToLabel
com.google.android.gms.internal.measurement.zzfr$zza$zzc: int zzf
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
kotlinx.coroutines.InvokeOnCancelling: int _invoked
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIME_ZONE_NAME
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SET_AS_GROUP_SUMMARY
com.google.android.gms.common.api.Status: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TEXT
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIMEOUT_AFTER
com.google.android.gms.internal.measurement.zzfy$zzj: com.google.android.gms.internal.measurement.zzfy$zzj zzc
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ENABLE_VIBRATION
com.google.android.gms.internal.measurement.zzgd$zza: com.google.android.gms.internal.measurement.zzkc zze
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzal
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DATA_URI
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency Daily
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer progress
com.google.android.gms.measurement.internal.zzae: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PLAY_SOUND
com.google.android.gms.internal.measurement.zzfo$zze: com.google.android.gms.internal.measurement.zzfo$zze zzc
com.google.android.gms.internal.measurement.zzfy$zzj: java.lang.String zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_BIG_TEXT
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mExpiredEventParams
com.google.android.gms.signin.internal.zai: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
androidx.media3.extractor.metadata.id3.CommentFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction Update
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int EXACT_ALARM_PERMISSION_REQUEST_CODE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_METHOD
androidx.media3.extractor.metadata.mp4.SmtaMetadataEntry: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzg: int zze
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.util.List actionInputs
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_ID
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzax
com.google.android.gms.internal.measurement.zzfy$zzj: int zzi
com.google.android.gms.internal.measurement.zzfy$zza: long zzi
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.google.android.gms.common.internal.GetServiceRequest: android.os.Parcelable$Creator CREATOR
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggerEventName
androidx.work.impl.utils.futures.AbstractFuture: androidx.work.impl.utils.futures.AbstractFuture$Listener listeners
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_ICON_ERROR_CODE
androidx.work.impl.utils.futures.AbstractFuture: java.lang.Object value
com.google.android.gms.internal.measurement.zzfr$zzg: com.google.android.gms.internal.measurement.zzll zzd
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String AUDIO_ATTRIBUTES_USAGE
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.dvbsi.AppInfoTable: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.id3.ChapterFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zza$zza: int zze
com.google.android.gms.internal.measurement.zzgd$zzd: java.lang.String zzi
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzi
com.google.android.gms.internal.measurement.zzfr$zzd: int zzh
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String INPUTS
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CACHE_DIRECTORY_BASE_PATH
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LARGE_ICON_ERROR_CODE
com.google.android.gms.common.zzq: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfo$zzb: boolean zzk
com.google.android.gms.internal.measurement.zzfo$zza: boolean zzi
androidx.media3.common.DrmInitData$SchemeData: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.mp4.SlowMotionData$Segment: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzkc zzh
com.google.android.gms.internal.measurement.zzfy$zze: com.google.android.gms.internal.measurement.zzfy$zze zzc
com.google.android.gms.internal.measurement.zzfy$zzk: long zzbg
com.google.android.gms.internal.measurement.zzfr$zzf: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.String contentTitle
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: com.dexterous.flutterlocalnotifications.models.BitmapSource bigPictureBitmapSource
com.google.android.gms.internal.measurement.zzfr$zzc: com.google.android.gms.internal.measurement.zzll zzd
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_LAUNCHED_APP
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.android.gms.cloudmessaging.zzd: android.os.Parcelable$Creator CREATOR
androidx.work.impl.utils.futures.AbstractFuture: androidx.work.impl.utils.futures.AbstractFuture$Waiter waiters
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer importance
androidx.work.impl.utils.futures.AbstractFuture$Waiter: androidx.work.impl.utils.futures.AbstractFuture$Waiter next
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_BLUE
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
com.google.android.gms.internal.measurement.zzfy$zzk: int zzs
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_FULL_SCREEN_INTENT_PERMISSION_METHOD
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_CALLBACK_HANDLE_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String timeZoneName
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int FORCE_DARK_ONLY
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String name
com.google.android.gms.internal.measurement.zzfy$zzj: int zze
com.google.android.gms.internal.measurement.zzgd$zzd: double zzk
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String description
com.google.android.gms.internal.measurement.zzfy$zza: com.google.android.gms.internal.measurement.zzll zzd
androidx.media3.extractor.metadata.id3.ChapterTocFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zza$zzf: com.google.android.gms.internal.measurement.zzfr$zza$zzf zzc
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_DETAILS
com.google.android.gms.internal.measurement.zzfo$zzb: com.google.android.gms.internal.measurement.zzfo$zzb zzc
com.google.android.gms.internal.measurement.zzfy$zzl: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzu
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] $VALUES
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.android.gms.internal.measurement.zzfo$zze: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.util.ArrayList messages
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int MEDIA_QUERY_ONLY
com.google.android.gms.internal.measurement.zzfr$zzd: int zze
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_NOTIFICATION_POLICY_ACCESS_METHOD
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTimedOutEventName
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
android.support.v4.media.MediaMetadataCompat: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SOUND_SOURCE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String VIBRATION_PATTERN
io.flutter.embedding.engine.FlutterOverlaySurface: int id
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzkc zzn
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mName
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String SECOND
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean setAsGroupSummary
com.google.android.gms.common.internal.ConnectionTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfo$zzc: com.google.android.gms.internal.measurement.zzfo$zzf zzf
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE
com.google.android.gms.internal.measurement.zzfo$zzd: java.lang.String zzi
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_BLUE
com.google.android.gms.internal.measurement.zzfy$zzk: long zzab
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INITIALIZE_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] $VALUES
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_ALPHA
com.google.android.gms.internal.measurement.zzjt: com.google.android.gms.internal.measurement.zzmj zzb
androidx.media3.extractor.metadata.icy.IcyInfo: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_PICTURE_BITMAP_SOURCE
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatBigText
com.google.android.gms.internal.measurement.zzfy$zzh: com.google.android.gms.internal.measurement.zzfy$zzh zzc
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean cancelNotification
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer number
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_ID
com.google.android.gms.internal.measurement.zzgd$zzb: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfo$zzf: int zze
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatSummaryText
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ICON
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
android.support.v4.media.session.PlaybackStateCompat: android.os.Parcelable$Creator CREATOR
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: boolean mActive
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzfy$zza zzbp
com.google.android.gms.measurement.internal.zzno: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
com.google.android.gms.internal.measurement.zzfy$zzd: int zzf
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PENDING_NOTIFICATION_REQUESTS_METHOD
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Object bigPicture
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
com.google.android.gms.internal.measurement.zzfy$zzl: int zzf
com.google.android.gms.internal.measurement.zzfo$zzc: com.google.android.gms.internal.measurement.zzfo$zzc zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer priority
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer ledColor
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzao
com.google.android.gms.internal.measurement.zzfr$zza$zzc: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfr$zzi: java.lang.String zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_GREEN
com.google.android.gms.measurement.internal.zzaj: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SELECT_FOREGROUND_NOTIFICATION_ACTION
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULED_DATE_TIME
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer day
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean contextual
com.google.android.gms.internal.measurement.zzfy$zzh: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zza: com.google.android.gms.internal.measurement.zzfo$zza zzc
com.google.android.gms.internal.measurement.zzfr$zzg: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfo$zzb: boolean zzl
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_RED
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERIODICALLY_SHOW_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String title
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: java.lang.String TAG
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_APP_TRIGGER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.BitmapSource largeIconBitmapSource
androidx.media3.extractor.metadata.flac.VorbisComment: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
com.google.android.gms.internal.measurement.zzfo$zza: com.google.android.gms.internal.measurement.zzkc zzh
com.google.android.gms.auth.api.signin.GoogleSignInAccount: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfy$zzk: long zzj
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zze zzv
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MATCH_DATE_TIME_COMPONENTS
com.google.android.gms.internal.measurement.zzjt: java.util.Map zzc
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzfy$zzl zzas
com.google.android.gms.internal.measurement.zzgd$zza: com.google.android.gms.internal.measurement.zzgd$zza zzc
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String bigText
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
com.google.android.gms.internal.measurement.zzgd$zzb: com.google.android.gms.internal.measurement.zzgd$zzb zzc
com.google.android.gms.internal.measurement.zzfy$zzi: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzfy$zzi zzbc
com.google.android.gms.internal.measurement.zzfo$zza: int zze
com.google.android.gms.internal.measurement.zzfy$zzb: com.google.android.gms.internal.measurement.zzfy$zzb zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String STYLE_INFORMATION
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzkc zzh
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzkc zzh
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzkc zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean ongoing
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
com.google.android.gms.internal.measurement.zzfr$zzi: int zzi
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DayOfMonthAndTime
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean enableLights
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfy$zzf: int zze
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String CANCEL_NOTIFICATION
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CREATE_NOTIFICATION_CHANNEL_GROUP_METHOD
com.google.android.gms.internal.measurement.zzgd$zzc: com.google.android.gms.internal.measurement.zzgd$zzc zzc
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SCHEDULED_NOTIFICATIONS
com.google.android.gms.internal.measurement.zzfy$zzk: int zzaq
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
com.google.android.gms.internal.measurement.zzfy$zzk: long zzam
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean indeterminate
androidx.media3.container.MdtaMetadataEntry: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgd$zzc: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ENABLE_VIBRATION
com.google.android.gms.internal.measurement.zzfy$zzk: int zzf
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int WEB_SOURCE_AND_WEB_TRIGGER
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction CreateIfNotExists
io.flutter.plugin.platform.SingleViewPresentation: int viewId
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress permissionRequestProgress
com.google.android.gms.common.internal.RootTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfo$zze: boolean zzk
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String AUDIO_ATTRIBUTES_USAGE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_EXACT_ALARMS_PERMISSION_METHOD
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_ID
com.google.android.gms.internal.measurement.zzfy$zzb: int zze
com.google.android.gms.internal.measurement.zzfo$zze: java.lang.String zzg
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggeredTimestamp
com.dexterous.flutterlocalnotifications.models.NotificationDetails: long[] vibrationPattern
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String MINUTE
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
com.google.android.gms.internal.measurement.zzfy$zzh: float zzi
com.google.android.gms.internal.measurement.zzgd$zzd: com.google.android.gms.internal.measurement.zzkc zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean channelBypassDnd
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzkc zzg
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Boolean important
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency Weekly
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_PICTURE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzaw
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzkc zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean channelShowBadge
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer visibility
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BODY
androidx.media3.extractor.metadata.flac.PictureFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer maxProgress
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_RED
com.google.android.gms.internal.measurement.zzgd$zza: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfr$zzg: com.google.android.gms.internal.measurement.zzfr$zzg zzc
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource RawResource
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_ALPHA
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SUMMARY_TEXT
com.google.android.gms.internal.measurement.zzfy$zzf: long zzh
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.google.android.gms.internal.measurement.zzfy$zzk: long zzk
androidx.media3.common.DrmInitData: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode exact
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.util.Map labelToSubtype
com.google.android.gms.internal.measurement.zzgd$zzc: int zze
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ENABLE_LIGHTS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean silent
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents Time
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction channelAction
com.google.android.gms.internal.measurement.zzfo$zzc: com.google.android.gms.internal.measurement.zzll zzd
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.google.android.gms.internal.measurement.zzfr$zzc: int zzi
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
com.google.android.gms.internal.measurement.zzfy$zzl: com.google.android.gms.internal.measurement.zzfy$zzl zzc
com.google.android.gms.cloudmessaging.CloudMessage: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfy$zzl: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LED_DETAILS_ERROR_CODE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CAN_SCHEDULE_EXACT_NOTIFICATIONS_METHOD
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatContentTitle
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String NAME
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_ALPHA
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzkc zzbe
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String IMPORTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String STYLE
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int PREFER_MEDIA_QUERY_OVER_FORCE_DARK
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_KEY
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String contentTitle
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ticker
com.google.android.gms.internal.measurement.zzfr$zza$zza: com.google.android.gms.internal.measurement.zzfr$zza$zza zzc
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzv
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzj
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzh
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzl
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationStyle style
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean autoCancel
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzfy$zzc zzbn
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zza zzr
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.DateTimeComponents matchDateTimeComponents
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_BLUE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_TEXT
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] $VALUES
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED_WITHOUT_APP_IDENTITY
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ADDITIONAL_FLAGS
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle BigText
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DELETE_NOTIFICATION_CHANNEL_GROUP_METHOD
com.google.android.gms.common.internal.TelemetryData: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String id
com.google.android.gms.internal.measurement.zzfr$zzb: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String WHEN
com.google.android.gms.internal.measurement.zzfr$zza: boolean zzi
android.support.v4.os.ResultReceiver: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.lang.String conversationTitle
com.google.android.gms.internal.measurement.zzfo$zza: boolean zzj
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
androidx.media3.container.Mp4TimestampData: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CONFIGURE_PARTITIONED_COOKIES
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] $VALUES
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: int MODULE_VERSION
com.google.android.gms.internal.measurement.zzfr$zzc: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.String summaryText
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_ARRAY_BUFFER
io.flutter.embedding.engine.FlutterJNI: float displayWidth
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.content.Context applicationContext
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatSummaryText
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String id
com.google.android.gms.common.api.Scope: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zzh: int zze
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_GREEN
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer minute
com.google.android.gms.internal.measurement.zzfy$zzk: long zzn
android.support.v4.media.MediaDescriptionCompat: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzfy$zzg: int zze
com.google.android.gms.internal.measurement.zzfr$zzh: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfy$zzh: long zzh
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.google.android.gms.common.zzo: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String NUMBER
com.google.android.gms.internal.measurement.zzfy$zzo: double zzk
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERIODICALLY_SHOW_WITH_DURATION_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String body
com.google.android.gms.internal.measurement.zzfy$zzk: long zzw
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.google.android.gms.common.Feature: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbf
com.google.android.gms.internal.measurement.zzfy$zza: long zzm
com.google.android.gms.internal.measurement.zzfy$zzk: int zzai
com.google.android.gms.internal.measurement.zzfo$zze: com.google.android.gms.internal.measurement.zzfo$zzc zzh
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SOUND
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzfo$zzb: boolean zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHOW_WHEN
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzk
kotlinx.coroutines.CompletedExceptionally: int _handled
com.google.android.gms.internal.measurement.zzfy$zzd: com.google.android.gms.internal.measurement.zzfy$zzm zzh
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INPUT
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SOUND
com.google.android.gms.measurement.internal.zzbf: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHOW_PROGRESS
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Media
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Integer titleColor
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry$VariantInfo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfo$zzf: com.google.android.gms.internal.measurement.zzfo$zzf zzc
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzfo$zzb: java.lang.String zzg
kotlinx.coroutines.channels.BufferedChannel: long receivers
com.google.android.gms.internal.measurement.zzfy$zzf: java.lang.String zzg
com.google.android.gms.measurement.internal.zzon: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgd$zzd: com.google.android.gms.internal.measurement.zzgd$zzd zzc
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.Long timestamp
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String IMPORTANT
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode alarmClock
com.google.android.gms.internal.measurement.zzfy$zzk: int zzay
com.google.android.gms.internal.measurement.zzfr$zze: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfo$zzf: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_NAME
com.google.android.gms.internal.measurement.zzfr$zzi: com.google.android.gms.internal.measurement.zzfr$zzi zzc
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Boolean bot
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String sound
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_BYPASS_DND
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DISPATCHER_HANDLE
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
com.google.android.gms.internal.measurement.zzfy$zze: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency scheduledNotificationRepeatFrequency
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.SoundSource soundSource
com.google.android.gms.internal.measurement.zzfo$zza: com.google.android.gms.internal.measurement.zzll zzd
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval EveryMinute
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
com.google.android.gms.internal.measurement.zzfy$zzo: int zze
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource BitmapFilePath
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ICON_SOURCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
com.google.android.gms.internal.measurement.zzfr$zzd: com.google.android.gms.internal.measurement.zzfr$zzi zzt
com.google.android.gms.internal.measurement.zzfo$zzb: int zzf
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatLines
io.flutter.embedding.engine.FlutterJNI: float displayDensity
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
com.dexterous.flutterlocalnotifications.models.MessageDetails: com.dexterous.flutterlocalnotifications.models.PersonDetails person
com.google.android.gms.internal.measurement.zzfy$zzj: java.lang.String zzg
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: java.lang.ClassLoader sClassLoader
com.google.android.gms.internal.measurement.zzfy$zzf: int zzj
com.google.android.gms.internal.measurement.zzfo$zzf: boolean zzh
com.google.android.gms.signin.internal.zaa: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ICON
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
androidx.media3.extractor.metadata.id3.MlltFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfr$zza$zzf: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String sound
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int PRERENDER_ENABLED
com.google.android.gms.internal.measurement.zzfy$zzh: java.lang.String zzf
androidx.media3.extractor.metadata.emsg.EventMessage: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SILENT
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long timeoutAfter
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: java.util.Map registeredPlugins
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction channelAction
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
com.google.android.gms.internal.measurement.zzfr$zze: com.google.android.gms.internal.measurement.zzfr$zze zzc
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.google.android.gms.internal.measurement.zzfy$zzg: com.google.android.gms.internal.measurement.zzfy$zzg zzc
com.google.android.gms.internal.measurement.zzfo$zze: boolean zzi
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Inbox
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean chronometerCountDown
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzk
com.google.android.gms.internal.measurement.zzfy$zzk: boolean zzbb
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzaa
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
androidx.media3.extractor.metadata.id3.BinaryFrame: android.os.Parcelable$Creator CREATOR
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfy$zzk: int zzac
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_CHANNELS_METHOD
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzba
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PAYLOAD
com.google.android.gms.internal.measurement.zzfy$zzf: com.google.android.gms.internal.measurement.zzfy$zzf zzc
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mExpiredEventName
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean enableLights
com.google.android.gms.internal.measurement.zzfy$zzk: long zzav
com.google.android.gms.internal.measurement.zzfy$zzl: int zze
com.google.android.gms.internal.measurement.zzfo$zzc: com.google.android.gms.internal.measurement.zzfo$zzd zzg
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer importance
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.google.android.gms.common.internal.zav: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int NOTIFICATION_PERMISSION_REQUEST_CODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean fullScreenIntent
com.google.android.gms.internal.measurement.zzfy$zzo: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] $VALUES
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATIONS_ERROR_MESSAGE
com.google.android.gms.internal.measurement.zzfy$zzc: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.internal.measurement.zzfy$zza: java.lang.String zzh
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzah
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DATA_MIME_TYPE
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.lang.Boolean groupConversation
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERMISSION_REQUEST_IN_PROGRESS_ERROR_MESSAGE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_TITLE
com.google.android.gms.internal.measurement.zzfy$zzk: int zzg
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_GREEN
com.google.firebase.messaging.RemoteMessage: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.ScheduleMode scheduleMode
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SHOW_BADGE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TAG
com.google.android.gms.internal.measurement.zzgd$zzb: int zze
com.google.android.gms.internal.measurement.zzfy$zzg: long zzg
com.google.android.gms.internal.measurement.zzfr$zzf: int zzh
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.lang.String LIBRARY_NAME
com.google.android.gms.internal.measurement.zzfy$zzn: com.google.android.gms.internal.measurement.zzll zzd
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggerTimeout
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SELECT_NOTIFICATION
com.google.android.gms.internal.measurement.zzfy$zzk: int zze
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource ByteArray
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.google.gson.Gson gson
com.google.android.gms.internal.measurement.zzfo$zzd: com.google.android.gms.internal.measurement.zzfo$zzd zzc
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer id
com.google.android.gms.internal.measurement.zzfy$zze: int zze
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzy
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
com.google.android.gms.internal.measurement.zzfy$zzo: float zzj
com.google.android.gms.internal.measurement.zzfo$zzb: com.google.android.gms.internal.measurement.zzfo$zzd zzj
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzka zzat
com.google.android.gms.internal.measurement.zzgd$zzb: com.google.android.gms.internal.measurement.zzll zzd
android.support.v4.media.MediaBrowserCompat$MediaItem: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
com.google.android.gms.internal.measurement.zzfr$zzf: int zzf
com.google.android.gms.internal.measurement.zzfy$zzb: java.lang.String zzl
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTimedOutEventParams
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzjz zze
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String id
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LARGE_ICON_BITMAP_SOURCE
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Daily
com.google.android.gms.internal.measurement.zzfy$zzc: com.google.android.gms.internal.measurement.zzfy$zzc zzc
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int NOTIFICATION_POLICY_ACCESS_REQUEST_CODE
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzae
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer audioAttributesUsage
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_NOTIFICATION
androidx.work.impl.utils.futures.AbstractFuture$Waiter: java.lang.Thread thread
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] $VALUES
com.google.android.gms.internal.measurement.zzfy$zzk: com.google.android.gms.internal.measurement.zzkc zzi
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzfr$zza zzc
com.google.android.gms.internal.measurement.zzfr$zzf: int zze
com.google.android.gms.internal.measurement.zzfr$zzb: com.google.android.gms.internal.measurement.zzfr$zzb zzc
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode exactAllowWhileIdle
com.google.android.gms.internal.measurement.zzfy$zzk: java.lang.String zzbk
com.google.android.gms.internal.measurement.zzfy$zzm: com.google.android.gms.internal.measurement.zzjz zzf
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
com.google.firebase.datatransport.TransportRegistrar: java.lang.String LIBRARY_NAME
com.google.android.gms.internal.measurement.zzfr$zza: com.google.android.gms.internal.measurement.zzll zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DAY
androidx.media3.extractor.metadata.mp4.MotionPhotoMetadata: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_WEB_TRIGGER
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Default
com.google.android.gms.internal.measurement.zzib: int zza
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Object icon
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String shortcutId
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: DynamiteModule$DynamiteLoaderClassLoader()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface: java.util.List getCookieInfo(java.lang.String)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory: RtspMediaSource$Factory()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void show(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurface2()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readInboxStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsentThirdParty(android.os.Bundle,long)
androidx.lifecycle.ReportFragment: ReportFragment()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setDescription(android.media.MediaDescription$Builder,java.lang.CharSequence)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api31: void setLogSessionIdOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.analytics.PlayerId)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readProgressInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType: com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType valueOf(java.lang.String)
androidx.core.app.NotificationCompat$BubbleMetadata$Api29Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
androidx.work.impl.utils.NetworkApi21: void unregisterNetworkCallbackCompat(android.net.ConnectivityManager,android.net.ConnectivityManager$NetworkCallback)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.SoundSource: SoundSource(java.lang.String,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle setConversationTitle(android.app.Notification$MessagingStyle,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.core.app.NotificationManagerCompat$Api26Impl: java.util.List getNotificationChannels(android.app.NotificationManager)
androidx.core.app.AlarmManagerCompat$Api23Impl: void setAndAllowWhileIdle(android.app.AlarmManager,int,long,android.app.PendingIntent)
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status[] values()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void proceed(boolean)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getDescription(android.media.MediaDescription)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
com.google.android.gms.internal.measurement.zzfr$zza$zze: com.google.android.gms.internal.measurement.zzfr$zza$zze[] values()
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: ModuleDescriptor()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebauthnSupport()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.activity.Api34Impl: float touchY(android.window.BackEvent)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory registerSubtype(java.lang.Class,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] $values()
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy[] values()
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void removeWebMessageListener(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.google.gson.TypeAdapter create(com.google.gson.Gson,com.google.gson.reflect.TypeToken)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode valueOf(java.lang.String)
android.support.v4.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.AlarmManager getAlarmManager(android.content.Context)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceRequest(android.webkit.WebResourceRequest)
com.google.gson.internal.bind.TypeAdapters$11: TypeAdapters$11()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getSessionId(com.google.android.gms.internal.measurement.zzdo)
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannelGroup(android.app.NotificationManager,android.app.NotificationChannelGroup)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readChannelInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
androidx.core.os.UserManagerCompat$Api24Impl: boolean isUserUnlocked(android.content.Context)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCachedAppInstanceId(com.google.android.gms.internal.measurement.zzdo)
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api21: boolean registerOnBufferAvailableListener(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter,androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$MediaCodecRendererCodecAdapterListener)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void showInterstitial(boolean)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readBigPictureStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface2()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
com.dexterous.flutterlocalnotifications.utils.BooleanUtils: BooleanUtils()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getActiveNotificationMessagingStyle(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory of(java.lang.Class,java.lang.String)
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy[] values()
android.support.v4.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api31: void setLogSessionIdToMediaCodecFormat(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter$Configuration,androidx.media3.exoplayer.analytics.PlayerId)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDarkBehavior()
androidx.core.app.ActivityCompat$Api23Impl: void onSharedElementsReady(java.lang.Object)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void onRoutingChanged(android.media.AudioRouting)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setColor(android.app.Notification$Builder,int)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: AnalyticsConnectorRegistrar()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setLocalOnly(android.app.Notification$Builder,boolean)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.net.Uri cache(byte[],java.lang.String,java.lang.String)
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebViewClient(android.webkit.WebViewClient)
com.google.firebase.analytics.FirebaseAnalytics: void setCurrentScreen(android.app.Activity,java.lang.String,java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readBigTextStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: MessagingStyleInformation(com.dexterous.flutterlocalnotifications.models.PersonDetails,java.lang.String,java.lang.Boolean,java.util.ArrayList,java.lang.Boolean,java.lang.Boolean)
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName valueOf(java.lang.String)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Action$Builder createBuilder(android.graphics.drawable.Icon,java.lang.CharSequence,android.app.PendingIntent)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String getNextFireDateMatchingDateTimeComponents(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void pendingNotificationRequests(io.flutter.plugin.common.MethodChannel$Result)
androidx.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebViewMediaIntegrityApiDefaultStatus()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void unregister(android.media.AudioTrack)
androidx.work.Worker: Worker(android.content.Context,androidx.work.WorkerParameters)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
io.flutter.embedding.engine.FlutterJNI: void endFrame2()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.activity.OnBackPressedDispatcher$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedError(android.webkit.WebView,android.webkit.WebResourceRequest,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.media3.exoplayer.ExoPlayerImpl$Api31: androidx.media3.exoplayer.analytics.PlayerId registerMediaMetricsListener(android.content.Context,androidx.media3.exoplayer.ExoPlayerImpl,boolean,java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserId(java.lang.String,long)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery: TransportBackendDiscovery()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getSpeculativeLoadingStatus()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
com.google.firebase.messaging.FirebaseMessagingKtxRegistrar: FirebaseMessagingKtxRegistrar()
vn.hunghd.flutterdownloader.DownloadWorker: DownloadWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.work.impl.background.systemjob.SystemJobService$Api28Impl: android.net.Network getNetwork(android.app.job.JobParameters)
com.google.gson.internal.sql.SqlTimestampTypeAdapter$1: SqlTimestampTypeAdapter$1()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
org.chromium.support_lib_boundary.PrefetchOperationResultBoundaryInterface: int getStatusCode()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
com.ryanheise.just_audio.AudioPlayer$ProcessingState: com.ryanheise.just_audio.AudioPlayer$ProcessingState valueOf(java.lang.String)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$getPluginConstantsForFirebaseApp$0(com.google.firebase.FirebaseApp,com.google.android.gms.tasks.TaskCompletionSource)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier[] values()
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails from(java.util.Map)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
com.google.firebase.messaging.reporting.MessagingClientEvent$Event: com.google.firebase.messaging.reporting.MessagingClientEvent$Event[] values()
com.google.android.gms.internal.measurement.zzga: com.google.android.gms.internal.measurement.zzga[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
androidx.core.app.RemoteInput$Api29Impl: android.app.RemoteInput$Builder setEditChoicesBeforeSending(android.app.RemoteInput$Builder,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setExtras(android.media.MediaDescription$Builder,android.os.Bundle)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNextRepeatingNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.firebase.FirebaseCommonRegistrar: FirebaseCommonRegistrar()
com.spencerccf.app_settings.AppSettingsPlugin: AppSettingsPlugin()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setBubbleMetadata(android.app.Notification$Builder,android.app.Notification$BubbleMetadata)
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
io.endigo.plugins.pdfviewflutter.PDFViewFlutterPlugin: PDFViewFlutterPlugin()
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivitySaveInstanceState(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzdo,long)
androidx.work.impl.utils.futures.DirectExecutor: androidx.work.impl.utils.futures.DirectExecutor valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
com.google.firebase.concurrent.ExecutorsRegistrar: ExecutorsRegistrar()
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: DateTimeComponents(java.lang.String,int)
androidx.core.app.AlarmManagerCompat$Api23Impl: void setExactAndAllowWhileIdle(android.app.AlarmManager,int,long,android.app.PendingIntent)
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface: java.lang.String getNoVarySearchHint()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProxyController()
com.google.gson.internal.sql.SqlDateTypeAdapter$1: SqlDateTypeAdapter$1()
androidx.activity.OnBackPressedDispatcher$Api33Impl: void registerOnBackInvokedCallback(java.lang.Object,int,java.lang.Object)
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State[] values()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setTitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
com.dexterous.flutterlocalnotifications.models.MessageDetails: MessageDetails(java.lang.String,java.lang.Long,com.dexterous.flutterlocalnotifications.models.PersonDetails,java.lang.String,java.lang.String)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void cancelPrefetch(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setPublicVersion(android.app.Notification$Builder,android.app.Notification)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void showNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.core.app.RemoteInput$Api26Impl: android.app.RemoteInput$Builder setAllowDataType(android.app.RemoteInput$Builder,java.lang.String,boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform: com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setLargeIcon(android.app.Notification$Builder,android.graphics.drawable.Icon)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void applyGrouping(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus valueOf(java.lang.String)
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode[] values()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setDeclineButtonColorHint(android.app.Notification$CallStyle,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map describeIcon(androidx.core.graphics.drawable.IconCompat)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onAttachedToEngine(io.flutter.embedding.engine.plugins.FlutterPlugin$FlutterPluginBinding)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebMessagePort(java.lang.Object)
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: BigPictureStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource,java.lang.Boolean)
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: java.util.List getComponents()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityDestroyed(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setAllowSystemGeneratedContextualActions(android.app.Notification$Builder,boolean)
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void setContentDescription(android.app.Notification$BigPictureStyle,java.lang.CharSequence)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean isAlgorithmicDarkeningAllowed()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useExactAlarm()
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setUsage(android.media.AudioAttributes$Builder,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void resetAnalyticsData(long)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setShortcutId(android.app.Notification$Builder,java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin: WebViewFlutterPlugin()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSafeBrowsingResponse(java.lang.Object)
com.github.barteksc.pdfviewer.PDFView: void setMidZoom(float)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingAllowlist(java.util.Set,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAlgorithmicDarkeningAllowed(boolean)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStarted(com.google.android.gms.dynamic.IObjectWrapper,long)
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin: ConnectivityPlugin()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
com.google.gson.Strictness: com.google.gson.Strictness valueOf(java.lang.String)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSafeBrowsingEnabled(boolean)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroupSummary(android.app.Notification$Builder,boolean)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStopped(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.core.app.NotificationManagerCompat$Api26Impl: android.app.NotificationChannel getNotificationChannel(android.app.NotificationManager,java.lang.String)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.WebStorage getWebStorage()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setMessagingStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConditionalUserProperty(android.os.Bundle,long)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Builder setForegroundServiceBehavior(android.app.Notification$Builder,int)
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
com.github.barteksc.pdfviewer.PDFView: void setScrollHandle(com.github.barteksc.pdfviewer.scroll.ScrollHandle)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setInboxStyle(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.google.firebase.datatransport.TransportRegistrar: java.util.List getComponents()
io.flutter.plugins.videoplayer.VideoPlayerPlugin: VideoPlayerPlugin()
com.google.firebase.messaging.FirebaseMessaging: com.google.firebase.messaging.FirebaseMessaging getInstance(com.google.firebase.FirebaseApp)
com.google.android.gms.measurement.AppMeasurement: int getMaxUserProperties(java.lang.String)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getOrCreateProfile(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
vn.hunghd.flutterdownloader.DownloadStatus: vn.hunghd.flutterdownloader.DownloadStatus valueOf(java.lang.String)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
com.google.gson.internal.bind.TypeAdapters$2: TypeAdapters$2()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void clearProxyOverride(java.lang.Runnable,java.util.concurrent.Executor)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.webkit.WebChromeClient getWebChromeClient()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readSoundInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] $values()
com.google.common.base.Function: boolean equals(java.lang.Object)
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver: AlarmManagerSchedulerBroadcastReceiver()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setCanScheduleExactNotifications(io.flutter.plugin.common.MethodChannel$Result)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator[] values()
com.google.android.gms.internal.measurement.zzfr$zza$zzd: com.google.android.gms.internal.measurement.zzfr$zza$zzd[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void deleteNotificationChannel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setVerificationText(android.app.Notification$CallStyle,java.lang.CharSequence)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenName(com.google.android.gms.internal.measurement.zzdo)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenClass()
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.core.app.RemoteInput$Api26Impl: java.util.Set getAllowedDataTypes(java.lang.Object)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy[] values()
com.example.cepron.MainActivity: MainActivity()
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: RepeatInterval(java.lang.String,int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconUri(android.media.MediaDescription$Builder,android.net.Uri)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEvent(java.lang.String,java.lang.String,android.os.Bundle,boolean,boolean,long)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView2(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
com.google.android.gms.internal.measurement.zzfo$zzf$zzb: com.google.android.gms.internal.measurement.zzfo$zzf$zzb[] values()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
kotlin.collections.AbstractList: AbstractList()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
com.ryanheise.audio_session.AudioSessionPlugin: AudioSessionPlugin()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.core.app.NotificationCompat$BigPictureStyle$Api23Impl: void setBigLargeIcon(android.app.Notification$BigPictureStyle,android.graphics.drawable.Icon)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidRawSoundResource(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearMeasurementEnabled(long)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
com.shockwave.pdfium.PdfiumCore: java.lang.Long nativeGetFirstChildBookmark(long,java.lang.Long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logHealthData(int,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper)
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] $values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNextNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
com.github.barteksc.pdfviewer.PDFView: com.github.barteksc.pdfviewer.scroll.ScrollHandle getScrollHandle()
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerController()
com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat: com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority[] values()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getStatics()
androidx.media.app.NotificationCompat$Api21Impl: android.app.Notification$MediaStyle createMediaStyle()
com.github.barteksc.pdfviewer.PDFView: float getMidZoom()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy: ConstraintProxy$StorageNotLowProxy()
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int pendingDequeuedImages()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType[] values()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver: ConstraintProxyUpdateReceiver()
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setLocusId(android.app.Notification$Builder,java.lang.Object)
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor[] values()
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status valueOf(java.lang.String)
com.google.gson.internal.bind.TypeAdapters$17: TypeAdapters$17()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
com.shockwave.pdfium.PdfiumCore: long nativeLoadPage(long,int)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: android.os.Parcelable castToParcelable(android.graphics.drawable.Icon)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebMessagePort(java.lang.reflect.InvocationHandler)
androidx.work.NetworkType: androidx.work.NetworkType valueOf(java.lang.String)
com.google.android.gms.internal.measurement.zzjn: com.google.android.gms.internal.measurement.zzjn[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] $values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean isValidDrawableResource(android.content.Context,java.lang.String,io.flutter.plugin.common.MethodChannel$Result,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: void setLogSessionIdOnMediaDrmSession(android.media.MediaDrm,byte[],androidx.media3.exoplayer.analytics.PlayerId)
androidx.core.content.FileProvider$Api21Impl: java.io.File[] getExternalMediaDirs(android.content.Context)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder createBuilder()
com.google.android.gms.measurement.AppMeasurement: com.google.android.gms.measurement.AppMeasurement getInstance(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: long calculateRepeatIntervalMilliseconds(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy valueOf(java.lang.String)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.net.Uri getIconUri(android.media.MediaDescription)
androidx.media3.exoplayer.ExoPlayerImpl$Api23: boolean isSuitableAudioOutputPresentInAudioDeviceInfoList(android.content.Context,android.media.AudioDeviceInfo[])
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference,boolean)
androidx.core.content.ContextCompat$Api24Impl: java.io.File getDataDir(android.content.Context)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setSound(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.dexterous.flutterlocalnotifications.models.IconSource: IconSource(java.lang.String,int)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory of(java.lang.Class)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void initialize(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver: FlutterFirebaseMessagingReceiver()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getBlockNetworkLoads()
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException: GooglePlayServicesMissingManifestValueException()
androidx.media3.exoplayer.audio.MediaCodecAudioRenderer$Api23: void setAudioSinkPreferredDevice(androidx.media3.exoplayer.audio.AudioSink,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
com.google.gson.internal.bind.TypeAdapters$16: TypeAdapters$16()
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService: FlutterFirebaseMessagingBackgroundService()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
com.github.barteksc.pdfviewer.util.FitPolicy: com.github.barteksc.pdfviewer.util.FitPolicy valueOf(java.lang.String)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
com.shockwave.pdfium.PdfiumCore: int nativeGetPageCount(long)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
com.google.android.gms.common.SupportErrorDialogFragment: SupportErrorDialogFragment()
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName[] values()
androidx.media3.exoplayer.drm.DrmUtil$Api21: boolean isMediaDrmStateException(java.lang.Throwable)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererResponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
com.google.firebase.installations.FirebaseInstallationsRegistrar: FirebaseInstallationsRegistrar()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomBigContentView(android.app.Notification$Builder,android.widget.RemoteViews)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
android.support.v4.media.MediaDescriptionCompat$Api23Impl: android.net.Uri getMediaUri(android.media.MediaDescription)
com.google.gson.internal.bind.TypeAdapters$25: TypeAdapters$25()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
com.shockwave.pdfium.PdfiumCore: long[] nativeGetPageLinks(long)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription build(android.media.MediaDescription$Builder)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler addDocumentStartJavaScript(java.lang.String,java.lang.String[])
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: RuntimeTypeAdapterFactory(java.lang.Class,java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onAttachedToActivity(io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.NotificationManagerCompat getNotificationManager(android.content.Context)
androidx.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void deleteNotificationChannelGroup(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: void start(int,java.util.Collection,int)
com.google.firebase.messaging.threads.ThreadPriority: com.google.firebase.messaging.threads.ThreadPriority valueOf(java.lang.String)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setContentType(android.media.AudioAttributes$Builder,int)
com.google.android.gms.internal.measurement.zzms: com.google.android.gms.internal.measurement.zzms[] values()
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle[] values()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: NotificationChannelGroupDetails()
androidx.core.app.AlarmManagerCompat$Api21Impl: void setAlarmClock(android.app.AlarmManager,java.lang.Object,android.app.PendingIntent)
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService: JobInfoSchedulerService()
androidx.core.graphics.drawable.IconCompat: IconCompat()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
androidx.room.Index$Order: androidx.room.Index$Order[] values()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,java.lang.String)
com.google.gson.internal.bind.TypeAdapters$23: TypeAdapters$23()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setAnswerButtonColorHint(android.app.Notification$CallStyle,int)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setBigTextStyle(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.String getMediaId(android.media.MediaDescription)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowFileAccess()
androidx.core.app.RemoteInput$Api26Impl: void addDataResultToIntent(androidx.core.app.RemoteInput,android.content.Intent,java.util.Map)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setMediaStyle(androidx.core.app.NotificationCompat$Builder)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebSourceAsync(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getEnterpriseAuthenticationAppLinkPolicyEnabled()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Boolean sendNotificationPayloadMessage(android.content.Intent)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler createWebView(android.webkit.WebView)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.shockwave.pdfium.PdfiumCore: java.lang.Integer nativeGetDestPageIndex(long,long)
androidx.work.impl.WorkDatabase_Impl: WorkDatabase_Impl()
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: java.util.List getComponents()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setBigPictureStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.NotificationCompat$MessagingStyle$Message createMessage(android.content.Context,com.dexterous.flutterlocalnotifications.models.MessageDetails)
com.google.android.gms.common.api.internal.zzd: zzd()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy[] values()
com.google.android.gms.common.api.internal.zzb: zzb()
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$2(com.google.firebase.components.ComponentContainer)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void beginAdUnitExposure(java.lang.String,long)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setSortKey(android.app.Notification$Builder,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getDropDataProvider()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.work.NetworkType: androidx.work.NetworkType[] values()
androidx.work.impl.Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] values()
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.core.app.Person$Api28Impl: androidx.core.app.Person fromAndroidPerson(android.app.Person)
androidx.core.app.NotificationCompat$MessagingStyle$Api28Impl: android.app.Notification$MessagingStyle setGroupConversation(android.app.Notification$MessagingStyle,boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.fragment.app.DialogFragment: DialogFragment()
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.core.app.NotificationCompat$Style$Api24Impl: void setChronometerCountDown(android.widget.RemoteViews,int,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.graphics.drawable.IconCompat getIconFromSource(android.content.Context,java.lang.Object,com.dexterous.flutterlocalnotifications.models.IconSource)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
com.google.gson.internal.bind.TypeAdapters$22: TypeAdapters$22()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: void setServiceWorkerClient(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getAttributionBehavior()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
com.github.barteksc.pdfviewer.PDFView: float getMinZoom()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setProfile(java.lang.String)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] values()
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] values()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.core.app.NotificationManagerCompat$Api24Impl: boolean areNotificationsEnabled(android.app.NotificationManager)
androidx.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowFileAccess(boolean)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProfileStore()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: boolean deleteProfile(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.privacysandbox.ads.adservices.internal.AdServicesInfo$Extensions30Impl: int getAdServicesVersion()
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy valueOf(java.lang.String)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setMediaId(android.media.MediaDescription$Builder,java.lang.String)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: java.util.List getComponents()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface: void remove()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: java.lang.String getAsString()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void saveScheduledNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.gson.internal.bind.TypeAdapters$6: TypeAdapters$6()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserProperty(java.lang.String,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,boolean,long)
androidx.startup.InitializationProvider: InitializationProvider()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.core.view.ViewGroupCompat$Api21Impl: void setTransitionGroup(android.view.ViewGroup,boolean)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
com.github.barteksc.pdfviewer.PDFView: void setSpacing(int)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowContentAccess()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void checkCanScheduleExactAlarms(android.app.AlarmManager)
com.google.android.gms.internal.measurement.zzkd: com.google.android.gms.internal.measurement.zzkd[] values()
com.baseflow.permissionhandler.PermissionHandlerPlugin: PermissionHandlerPlugin()
androidx.activity.OnBackPressedDispatcher$Api33Impl: android.window.OnBackInvokedCallback createOnBackInvokedCallback(kotlin.jvm.functions.Function0)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
com.google.gson.internal.bind.TypeAdapters$27: TypeAdapters$27()
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: FirebaseCommonLegacyRegistrar()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.work.WorkManagerInitializer: WorkManagerInitializer()
com.google.android.gms.internal.measurement.zzfy$zzl$zzb: com.google.android.gms.internal.measurement.zzfy$zzl$zzb[] values()
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: java.util.List getComponents()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
com.google.firebase.analytics.FirebaseAnalytics: com.google.android.gms.measurement.internal.zzlb getScionFrontendApiImplementation(android.content.Context,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.core.view.ViewGroupCompat$Api21Impl: int getNestedScrollAxes(android.view.ViewGroup)
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.work.impl.background.systemalarm.RescheduleReceiver: RescheduleReceiver()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setCategory(android.app.Notification$Builder,java.lang.String)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forOngoingCall(android.app.Person,android.app.PendingIntent)
com.google.gson.reflect.TypeToken: TypeToken()
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void reverse(android.animation.AnimatorSet)
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.util.List getNotificationChannelGroups(android.app.NotificationManager)
com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform: com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityPaused(com.google.android.gms.dynamic.IObjectWrapper,long)
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void initSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onPageCommitVisible(android.webkit.WebView,java.lang.String)
com.google.gson.internal.bind.TypeAdapters$18: TypeAdapters$18()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.lang.String getId(android.app.NotificationChannel)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: java.lang.CharSequence getDescription()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getSafeBrowsingEnabled()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useAlarmClock()
com.shockwave.pdfium.PdfDocument$Meta: PdfDocument$Meta()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromActivityForConfigChanges()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onSafeBrowsingHit(android.webkit.WebView,android.webkit.WebResourceRequest,int,java.lang.reflect.InvocationHandler)
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType valueOf(java.lang.String)
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag[] values()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
com.google.gson.internal.bind.TypeAdapters$14: TypeAdapters$14()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addExtras(android.app.Notification$Action$Builder,android.os.Bundle)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readPlatformSpecifics(java.util.Map,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action build(android.app.Notification$Action$Builder)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType[] values()
androidx.media3.exoplayer.mediacodec.MediaCodecPerformancePointCoverageProvider$Api29: int areResolutionAndFrameRateCovered(android.media.MediaCodecInfo$VideoCapabilities,int,int,double)
com.google.android.gms.measurement.internal.zzje$zza: com.google.android.gms.measurement.internal.zzje$zza[] values()
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingPlugin: FlutterFirebaseMessagingPlugin()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface: boolean terminate()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder createBuilder(android.content.Context,java.lang.String)
com.google.android.gms.internal.measurement.zzbv: com.google.android.gms.internal.measurement.zzbv[] values()
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.util.List getComponents()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onActivityResult(int,int,android.content.Intent)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
com.google.android.gms.internal.measurement.zzgd$zzd$zza: com.google.android.gms.internal.measurement.zzgd$zzd$zza[] values()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.core.view.ViewGroupCompat$Api21Impl: boolean isTransitionGroup(android.view.ViewGroup)
com.google.android.gms.internal.measurement.zzdm: com.google.android.gms.internal.measurement.zzdj asInterface(android.os.IBinder)
com.google.firebase.analytics.FirebaseAnalytics: java.lang.String getFirebaseInstanceId()
com.shockwave.pdfium.PdfiumCore: java.lang.String nativeGetDocumentMetaText(long,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.lang.String getId(android.app.NotificationChannelGroup)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
androidx.core.app.AlarmManagerCompat$Api21Impl: android.app.AlarmManager$AlarmClockInfo createAlarmClockInfo(long,android.app.PendingIntent)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
com.google.android.gms.measurement.AppMeasurement: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler[] createWebMessageChannel()
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebChromeClient(android.webkit.WebChromeClient)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebResourceError(java.lang.reflect.InvocationHandler)
com.google.android.datatransport.cct.CctBackendFactory: com.google.android.datatransport.runtime.backends.TransportBackend create(com.google.android.datatransport.runtime.backends.CreationContext)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewRendererClient(java.lang.reflect.InvocationHandler)
androidx.work.CoroutineWorker: CoroutineWorker(android.content.Context,androidx.work.WorkerParameters)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.content.Intent getLaunchIntent(android.content.Context)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] values()
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.util.ArrayList readMessages(java.util.ArrayList)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy: ConstraintProxy$BatteryChargingProxy()
androidx.media3.exoplayer.dash.DashMediaSource$Factory: DashMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode valueOf(java.lang.String)
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor[] values()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getProfile(java.lang.String)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType valueOf(java.lang.String)
com.google.android.gms.measurement.internal.zznt: com.google.android.gms.measurement.internal.zznt[] values()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.os.Bundle getExtras(android.media.MediaDescription)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api23: void setPreferredDeviceOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType[] values()
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList createLocaleList(java.util.Locale[])
androidx.core.app.ActivityCompat$Api23Impl: void requestPermissions(android.app.Activity,java.lang.String[],int)
dev.fluttercommunity.plus.share.ShareFileProvider: ShareFileProvider()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setBackForwardCacheEnabled(boolean)
com.google.android.gms.common.api.GoogleApiActivity: GoogleApiActivity()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setBadgeIconType(android.app.Notification$Builder,int)
com.shockwave.pdfium.PdfPasswordException: PdfPasswordException()
io.flutter.embedding.engine.FlutterJNI: boolean IsSurfaceControlEnabled()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void performAction(android.os.Bundle,com.google.android.gms.internal.measurement.zzdo,long)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getAppInstanceId()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.media3.exoplayer.drm.DrmUtil$Api21: int mediaDrmStateExceptionToErrorCode(java.lang.Throwable)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice[] values()
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface: java.util.Map getAdditionalHeaders()
com.dexterous.flutterlocalnotifications.utils.StringUtils: java.lang.Boolean isNullOrEmpty(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerSourceAsync(android.net.Uri,android.view.InputEvent)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerWebTrigger(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest,kotlin.coroutines.Continuation)
androidx.core.app.NotificationManagerCompat$Api26Impl: void deleteNotificationChannel(android.app.NotificationManager,java.lang.String)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onReattachedToActivityForConfigChanges(io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererUnresponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebauthnSupport(int)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getTitle(android.media.MediaDescription)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomHeadsUpContentView(android.app.Notification$Builder,android.widget.RemoteViews)
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult[] values()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: AppMeasurementDynamiteService()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setSubtitle(android.media.MediaDescription$Builder,java.lang.CharSequence)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void clearPrefetch(java.lang.String,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler,android.os.Handler)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
com.google.android.gms.internal.measurement.zzfy$zzj$zzb: com.google.android.gms.internal.measurement.zzfy$zzj$zzb[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
com.shockwave.pdfium.PdfiumCore: android.graphics.Point nativePageCoordsToDevice(long,int,int,int,int,int,double,double)
com.google.common.collect.Ordering: Ordering()
androidx.core.app.ActivityCompat$Api23Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: int getErrorCode()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityCreated(com.google.android.gms.dynamic.IObjectWrapper,android.os.Bundle,long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setProgress(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: boolean isBluetoothConnected(android.media.AudioManager,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void hasNotificationPolicyAccess(io.flutter.plugin.common.MethodChannel$Result)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setCurrentScreen(com.google.android.gms.dynamic.IObjectWrapper,java.lang.String,java.lang.String,long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.Notification createNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.google.android.gms.common.api.internal.LifecycleCallback: com.google.android.gms.common.api.internal.LifecycleFragment getChimeraLifecycleFragmentImpl(com.google.android.gms.common.api.internal.LifecycleActivity)
androidx.work.impl.utils.NetworkApi21: android.net.NetworkCapabilities getNetworkCapabilitiesCompat(android.net.ConnectivityManager,android.net.Network)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] $values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.os.ConfigurationCompat$Api24Impl: void setLocales(android.content.res.Configuration,androidx.core.os.LocaleListCompat)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void close()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.models.NotificationDetails extractNotificationDetails(io.flutter.plugin.common.MethodChannel$Result,java.util.Map)
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setLegacyStreamType(android.media.AudioAttributes$Builder,int)
com.google.firebase.messaging.FirebaseMessagingService: FirebaseMessagingService()
androidx.core.app.ActivityCompat$Api32Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
com.google.gson.internal.Excluder: Excluder()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object deleteRegistrations(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest,kotlin.coroutines.Continuation)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String getNextFireDate(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancelAllNotifications(io.flutter.plugin.common.MethodChannel$Result)
com.google.android.gms.internal.measurement.zzs: com.google.android.gms.internal.measurement.zzs[] values()
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType[] values()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void backToSafety(boolean)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
com.dexterous.flutterlocalnotifications.models.ScheduleMode$Deserializer: ScheduleMode$Deserializer()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken[] values()
com.github.barteksc.pdfviewer.PDFView: void setPositionOffset(float)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessage(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWillSuppressErrorPage(boolean)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
androidx.media.AudioFocusRequestCompat$Api26Impl: android.media.AudioFocusRequest createInstance(int,android.media.AudioAttributes,boolean,android.media.AudioManager$OnAudioFocusChangeListener,android.os.Handler)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEventAndBundle(java.lang.String,java.lang.String,android.os.Bundle,com.google.android.gms.internal.measurement.zzdo,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenClass(com.google.android.gms.internal.measurement.zzdo)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType valueOf(java.lang.String)
com.github.barteksc.pdfviewer.PDFView: float getCurrentYOffset()
kotlin.collections.AbstractMutableSet: AbstractMutableSet()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: boolean onCreate()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver: ScheduledNotificationBootReceiver()
io.flutter.plugins.imagepicker.ImagePickerFileProvider: ImagePickerFileProvider()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setVisibility(android.app.Notification$Builder,int)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.work.ArrayCreatingInputMerger: ArrayCreatingInputMerger()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$1: FlutterLocalNotificationsPlugin$1()
androidx.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
android.support.v4.media.MediaDescriptionCompat$Api23Impl: void setMediaUri(android.media.MediaDescription$Builder,android.net.Uri)
androidx.media.app.NotificationCompat$Api21Impl: void setShowActionsInCompactView(android.app.Notification$MediaStyle,int[])
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
com.dexterous.flutterlocalnotifications.utils.BooleanUtils: boolean getValue(java.lang.Boolean)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean stop(java.io.OutputStream,java.util.concurrent.Executor)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.os.ConfigurationCompat$Api24Impl: android.os.LocaleList getLocales(android.content.res.Configuration)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin: DeviceInfoPlusPlugin()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.net.Uri retrieveSoundResourceUri(android.content.Context,java.lang.String,com.dexterous.flutterlocalnotifications.models.SoundSource)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setSound(android.app.Notification$Builder,android.net.Uri,java.lang.Object)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void isDataCollectionEnabled(com.google.android.gms.internal.measurement.zzdo)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.media.MediaDescription$Builder createBuilder()
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
com.dexterous.flutterlocalnotifications.models.Time: com.dexterous.flutterlocalnotifications.models.Time from(java.util.Map)
androidx.media.app.NotificationCompat$Api21Impl: android.app.Notification$MediaStyle fillInMediaStyle(android.app.Notification$MediaStyle,int[],android.support.v4.media.session.MediaSessionCompat$Token)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
com.github.barteksc.pdfviewer.PDFView: void setDefaultPage(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
com.github.barteksc.pdfviewer.util.SnapEdge: com.github.barteksc.pdfviewer.util.SnapEdge valueOf(java.lang.String)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestNotificationsPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.media3.exoplayer.hls.HlsMediaSource$Factory: HlsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
com.google.gson.internal.bind.TypeAdapters$21: TypeAdapters$21()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.core.app.Person$Api28Impl: android.app.Person toAndroidPerson(androidx.core.app.Person)
androidx.activity.OnBackPressedDispatcher$Api34Impl: android.window.OnBackInvokedCallback createOnBackAnimationCallback(kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function0,kotlin.jvm.functions.Function0)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
com.github.barteksc.pdfviewer.util.SnapEdge: com.github.barteksc.pdfviewer.util.SnapEdge[] values()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
androidx.core.app.NotificationCompat$CallStyle$Api21Impl: android.app.Notification$Builder setCategory(android.app.Notification$Builder,java.lang.String)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype[] values()
androidx.media.app.NotificationCompat$Api15Impl: void setContentDescription(android.widget.RemoteViews,int,java.lang.CharSequence)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertServiceWorkerSettings(java.lang.Object)
com.google.firebase.provider.FirebaseInitProvider: FirebaseInitProvider()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
com.google.gson.Gson$3: Gson$3()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode[] values()
com.google.android.gms.measurement.AppMeasurement: long generateEventId()
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestExactAlarmsPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDarkBehavior(int)
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: BigTextStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertServiceWorkerSettings(java.lang.reflect.InvocationHandler)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] values()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void addWebMessageListener(java.lang.String,java.lang.String[],java.lang.reflect.InvocationHandler)
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void release()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.activity.Api34Impl: int swipeEdge(android.window.BackEvent)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
vn.hunghd.flutterdownloader.DownloadStatus: vn.hunghd.flutterdownloader.DownloadStatus[] values()
androidx.work.OverwritingInputMerger: OverwritingInputMerger()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory: SsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean isTracing()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.text.Spanned fromHtml(java.lang.String)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void setClearCachedDataIntervalMs(int)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
com.dexterous.flutterlocalnotifications.models.NotificationStyle: NotificationStyle(java.lang.String,int)
dev.fluttercommunity.plus.share.SharePlusPlugin: SharePlusPlugin()
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
com.google.gson.internal.sql.SqlTimeTypeAdapter$1: SqlTimeTypeAdapter$1()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedHttpError(android.webkit.WebView,android.webkit.WebResourceRequest,android.webkit.WebResourceResponse)
androidx.fragment.app.FragmentContainerView: androidx.fragment.app.Fragment getFragment()
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
com.github.barteksc.pdfviewer.PDFView$ScrollDir: com.github.barteksc.pdfviewer.PDFView$ScrollDir[] values()
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat[] values()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler[] getPorts()
androidx.media3.exoplayer.drm.DrmUtil$Api23: boolean isMediaDrmResetException(java.lang.Throwable)
androidx.core.app.ActivityCompat$Api31Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
androidx.media3.exoplayer.ExoPlayerImpl$Api23: void registerAudioDeviceCallback(android.media.AudioManager,android.media.AudioDeviceCallback,android.os.Handler)
com.google.gson.internal.bind.JsonElementTypeAdapter: JsonElementTypeAdapter()
androidx.core.app.NotificationCompat$DecoratedCustomViewStyle$Api24Impl: android.app.Notification$Style createDecoratedCustomViewStyle()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void removeNotificationFromCache(android.content.Context,java.lang.Integer)
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy[] values()
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider: FlutterFirebaseMessagingInitProvider()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$Action$Builder setAuthenticationRequired(android.app.Notification$Action$Builder,boolean)
androidx.media3.exoplayer.video.MediaCodecVideoRenderer$Api26: boolean doesDisplaySupportDolbyVision(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api31: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.CookieManager getCookieManager()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getGmpAppId(com.google.android.gms.internal.measurement.zzdo)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: int getType()
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannel(android.app.NotificationManager,android.app.NotificationChannel)
androidx.work.impl.background.systemalarm.SystemAlarmService: SystemAlarmService()
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api28Impl: android.os.Parcelable castToParcelable(android.app.Person)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture getMeasurementApiStatusAsync()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromActivity()
com.google.gson.internal.bind.TypeAdapters$19: TypeAdapters$19()
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
com.google.android.gms.internal.measurement.zzfo$zzd$zzb: com.google.android.gms.internal.measurement.zzfo$zzd$zzb[] values()
com.github.barteksc.pdfviewer.PDFView: float getPositionOffset()
androidx.core.app.RemoteInput$Api26Impl: java.util.Map getDataResultsFromIntent(android.content.Intent,java.lang.String)
kotlin.collections.AbstractMutableList: AbstractMutableList()
com.google.android.gms.measurement.AppMeasurementReceiver: AppMeasurementReceiver()
androidx.media.AudioAttributesCompat: AudioAttributesCompat()
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: com.google.firebase.analytics.connector.AnalyticsConnector lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getOffscreenPreRaster()
androidx.media.AudioAttributesImplBase: AudioAttributesImplBase()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceError(java.lang.Object)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: long calculateNextNotificationTrigger(long,long)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle addMessage(android.app.Notification$MessagingStyle,android.app.Notification$MessagingStyle$Message)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void insertVisualStateCallback(long,java.lang.reflect.InvocationHandler)
com.google.android.gms.measurement.AppMeasurement: java.util.Map getUserProperties(java.lang.String,java.lang.String,boolean)
com.google.android.gms.measurement.AppMeasurement: void endAdUnitExposure(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDefaultEventParameters(android.os.Bundle)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState valueOf(java.lang.String)
com.google.gson.internal.bind.TypeAdapters$8: TypeAdapters$8()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.work.impl.utils.NetworkApi21: boolean hasCapabilityCompat(android.net.NetworkCapabilities,int)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
androidx.media3.datasource.FileDataSource$Api21: boolean isPermissionError(java.lang.Throwable)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forScreeningCall(android.app.Person,android.app.PendingIntent,android.app.PendingIntent)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAttributionBehavior(int)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.core.app.NotificationManagerCompat$Api26Impl: void deleteNotificationChannelGroup(android.app.NotificationManager,java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsent(android.os.Bundle,long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map describePerson(androidx.core.app.Person)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
com.shockwave.pdfium.PdfiumCore: java.lang.String nativeGetBookmarkTitle(long)
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel valueOf(java.lang.String)
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
android.support.v4.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Integer tryParseInt(java.lang.String)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSettings(android.webkit.WebSettings)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
com.ryanheise.just_audio.JustAudioPlugin: JustAudioPlugin()
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction valueOf(java.lang.String)
com.google.common.collect.BaseImmutableMultimap: BaseImmutableMultimap()
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy: ConstraintProxy$BatteryNotLowProxy()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,java.lang.reflect.InvocationHandler,android.webkit.ValueCallback)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.media.AudioManagerCompat$Api26Impl: int abandonAudioFocusRequest(android.media.AudioManager,android.media.AudioFocusRequest)
com.github.barteksc.pdfviewer.PDFView: void setAutoSpacing(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails from(java.util.Map)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: ScheduleMode(java.lang.String,int)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getGmpAppId()
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval valueOf(java.lang.String)
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel[] values()
androidx.room.Index$Order: androidx.room.Index$Order valueOf(java.lang.String)
androidx.core.app.NotificationCompat$CallStyle$Api28Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,android.app.Person)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readLedInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: InboxStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.util.ArrayList,java.lang.Boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
com.google.firebase.FirebaseCommonKtxRegistrar: java.util.List getComponents()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map extractNotificationResponseMap(android.content.Intent)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat: com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat[] values()
androidx.work.impl.WorkManagerImpl$Api24Impl: boolean isDeviceProtectedStorage(android.content.Context)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int getDrawableResourceId(android.content.Context,java.lang.String)
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Action$Builder setSemanticAction(android.app.Notification$Action$Builder,int)
androidx.core.content.ContextCompat$Api24Impl: android.content.Context createDeviceProtectedStorageContext(android.content.Context)
androidx.media.AudioAttributesImplApi26: AudioAttributesImplApi26()
com.google.firebase.messaging.FirebaseMessagingRegistrar: FirebaseMessagingRegistrar()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: android.graphics.Bitmap getIconBitmap(android.media.MediaDescription)
com.ryanheise.just_audio.AudioPlayer$ProcessingState: com.ryanheise.just_audio.AudioPlayer$ProcessingState[] values()
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setEnterpriseAuthenticationAppLinkPolicyEnabled(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void createNotificationChannelGroup(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNextNotificationMatchingDateComponents(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.gson.internal.bind.TypeAdapters$4: TypeAdapters$4()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebViewMediaIntegrityApiStatus(int,java.util.Map)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setVerificationIcon(android.app.Notification$CallStyle,android.graphics.drawable.Icon)
androidx.core.app.NotificationManagerCompat$Api24Impl: int getImportance(android.app.NotificationManager)
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus[] values()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
com.google.android.gms.measurement.AppMeasurement: void setConditionalUserProperty(com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty)
com.google.gson.internal.sql.SqlTimeTypeAdapter: SqlTimeTypeAdapter()
com.google.gson.internal.bind.TypeAdapters$3: TypeAdapters$3()
com.shockwave.pdfium.R: R()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void registerOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzdp)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebChromeClient getWebChromeClient()
com.shockwave.pdfium.PdfiumCore: com.shockwave.pdfium.util.Size nativeGetPageSizeByIndex(long,int,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean launchedActivityFromHistory(android.content.Intent)
com.dexterous.flutterlocalnotifications.models.styles.StyleInformation: StyleInformation()
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: com.google.common.collect.ImmutableSet getAllBluetoothDeviceTypes()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
com.github.barteksc.pdfviewer.PDFView: void setPageFitPolicy(com.github.barteksc.pdfviewer.util.FitPolicy)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Action$Builder setAuthenticationRequired(android.app.Notification$Action$Builder,boolean)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.String getData()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: NotificationDetails()
com.github.barteksc.pdfviewer.util.FitPolicy: com.github.barteksc.pdfviewer.util.FitPolicy[] values()
com.shockwave.pdfium.BuildConfig: BuildConfig()
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag[] values()
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory registerSubtype(java.lang.Class)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.Bundle call(java.lang.String,java.lang.String,android.os.Bundle)
com.github.barteksc.pdfviewer.PDFView: void setSwipeEnabled(boolean)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMinimumSessionDuration(long)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setIsVideo(android.app.Notification$CallStyle,boolean)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture deleteRegistrationsAsync(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebkitToCompatConverter()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.work.impl.utils.futures.DirectExecutor: androidx.work.impl.utils.futures.DirectExecutor[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface: void onComplete(long)
com.google.android.datatransport.cct.CctBackendFactory: CctBackendFactory()
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: NotificationChannelAction(java.lang.String,int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
com.shockwave.pdfium.PdfiumCore: android.graphics.RectF nativeGetLinkRect(long)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
com.google.gson.Strictness: com.google.gson.Strictness[] values()
com.google.firebase.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
com.github.barteksc.pdfviewer.PDFView$ScrollDir: com.github.barteksc.pdfviewer.PDFView$ScrollDir valueOf(java.lang.String)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator valueOf(java.lang.String)
androidx.media.app.NotificationCompat$Api21Impl: void setMediaStyle(android.app.Notification$Builder,android.app.Notification$MediaStyle)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getProfile()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
com.google.android.gms.internal.measurement.zzco: com.google.android.gms.internal.measurement.zzco[] values()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readLargeIconInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setSettingsText(android.app.Notification$Builder,java.lang.CharSequence)
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: int getMaxSupportedChannelCountForPassthrough(int,int,androidx.media3.common.AudioAttributes)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebTriggerAsync(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor)
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy[] values()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
com.google.android.gms.measurement.AppMeasurementService: AppMeasurementService()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState[] values()
com.shockwave.pdfium.PdfDocument: PdfDocument()
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.google.gson.Gson buildGson()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setAudioMuted(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onRequestPermissionsResult(int,java.lang.String[],int[])
com.github.barteksc.pdfviewer.PDFView: float getMaxZoom()
com.google.firebase.messaging.reporting.MessagingClientEvent$Event: com.google.firebase.messaging.reporting.MessagingClientEvent$Event valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
com.google.gson.internal.bind.TypeAdapters$7: TypeAdapters$7()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void postMessage(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
com.google.gson.internal.bind.TypeAdapters$20: TypeAdapters$20()
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.work.impl.utils.NetworkApi23: android.net.Network getActiveNetworkCompat(android.net.ConnectivityManager)
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason[] values()
com.google.gson.internal.bind.TypeAdapters$13: TypeAdapters$13()
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera[] values()
androidx.media3.datasource.RawResourceDataSource: android.net.Uri buildRawResourceUri(int)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityResumed(com.google.android.gms.dynamic.IObjectWrapper,long)
com.google.android.gms.internal.measurement.zzcn: com.google.android.gms.internal.measurement.zzcn[] values()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMeasurementEnabled(boolean,long)
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
com.google.gson.internal.sql.SqlDateTypeAdapter: SqlDateTypeAdapter()
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void showBigPictureWhenCollapsed(android.app.Notification$BigPictureStyle,boolean)
com.google.gson.Gson$FutureTypeAdapter: Gson$FutureTypeAdapter()
com.github.barteksc.pdfviewer.PDFView: void setSwipeVertical(boolean)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: boolean isAudioMuted()
androidx.media3.common.AudioAttributes$Api29: void setAllowedCapturePolicy(android.media.AudioAttributes$Builder,int)
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.webkit.ValueCallback)
android.support.v4.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode valueOf(java.lang.String)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String[] getStreamTypes(android.net.Uri,java.lang.String)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
com.google.gson.internal.bind.TypeAdapters$15: TypeAdapters$15()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readMessagingStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationDetails from(java.util.Map)
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode valueOf(java.lang.String)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
com.shockwave.pdfium.PdfiumCore: void nativeCloseDocument(long)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.work.impl.background.systemjob.SystemJobService$Api24Impl: java.lang.String[] getTriggeredContentAuthorities(android.app.job.JobParameters)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean shouldOverrideUrlLoading(android.webkit.WebView,android.webkit.WebResourceRequest)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getNotificationChannels(io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
com.shockwave.pdfium.PdfiumCore: long nativeGetBookmarkDestIndex(long,long)
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle createMessagingStyle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void stopForegroundService(io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object getMeasurementApiStatus(kotlin.coroutines.Continuation)
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] $values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDark()
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface: void onPostMessage(android.webkit.WebView,java.lang.reflect.InvocationHandler,android.net.Uri,boolean,java.lang.reflect.InvocationHandler)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void processForegroundNotificationAction(android.content.Intent,java.util.Map)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipPath(android.graphics.Path)
android.support.v4.media.MediaDescriptionCompat$Api21Impl: java.lang.CharSequence getSubtitle(android.media.MediaDescription)
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: void setLargeIcon(android.app.Notification$Builder,android.graphics.drawable.Icon)
androidx.webkit.WebViewClientCompat: WebViewClientCompat()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidIcon(io.flutter.plugin.common.MethodChannel$Result,java.lang.String)
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera valueOf(java.lang.String)
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy: ConstraintProxy$NetworkStateProxy()
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes build(android.media.AudioAttributes$Builder)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.common.base.Function: java.lang.Object apply(java.lang.Object)
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType: com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType[] values()
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag valueOf(java.lang.String)
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status[] values()
android.support.v4.media.MediaDescriptionCompat$Api21Impl: void setIconBitmap(android.media.MediaDescription$Builder,android.graphics.Bitmap)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void repeat(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin: WakelockPlusPlugin()
com.google.android.gms.internal.measurement.zzln: com.google.android.gms.internal.measurement.zzln[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
com.github.barteksc.pdfviewer.PDFView: java.util.List getTableOfContents()
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.plugins.webviewflutter.JavaScriptChannel: void postMessage(java.lang.String)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$didReinitializeFirebaseCore$1(com.google.android.gms.tasks.TaskCompletionSource)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: boolean requiresSecureDecoder(android.media.MediaDrm,java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getWillSuppressErrorPage()
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
com.google.gson.internal.bind.TypeAdapters$1: TypeAdapters$1()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidLedDetails(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.utils.StringUtils: StringUtils()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidLargeIcon(io.flutter.plugin.common.MethodChannel$Result,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource)
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode[] values()
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
com.google.firebase.messaging.FirebaseMessagingRegistrar: java.util.List getComponents()
androidx.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getConditionalUserProperties(java.lang.String,java.lang.String,com.google.android.gms.internal.measurement.zzdo)
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: ScheduledNotificationReceiver()
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useAllowWhileIdle()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidBigPictureResources(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupAlarm(com.dexterous.flutterlocalnotifications.models.NotificationDetails,android.app.AlarmManager,long,android.app.PendingIntent)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onMethodCall(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerWebSource(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest,kotlin.coroutines.Continuation)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.core.content.ContextCompat$Api24Impl: boolean isDeviceProtectedStorage(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.work.impl.foreground.SystemForegroundService$Api31Impl: void startForeground(android.app.Service,int,android.app.Notification,int)
com.google.firebase.iid.FirebaseInstanceIdReceiver: FirebaseInstanceIdReceiver()
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioDeviceInfoApi23 getDefaultRoutedDeviceForAttributes(android.media.AudioManager,androidx.media3.common.AudioAttributes)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerTriggerAsync(android.net.Uri)
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State valueOf(java.lang.String)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.view.View getView()
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$1(com.google.firebase.components.ComponentContainer)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getWebViewMediaIntegrityApiOverrideRules()
com.google.android.gms.internal.measurement.zzbx: android.os.IBinder asBinder()
com.google.android.gms.measurement.internal.zzjg: com.google.android.gms.measurement.internal.zzjg[] values()
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenName()
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setTimeoutAfter(android.app.Notification$Builder,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.database.Cursor query(android.net.Uri,java.lang.String[],java.lang.String,java.lang.String[],java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.core.app.RemoteInput$Api20Impl: void addResultsToIntent(java.lang.Object,android.content.Intent,android.os.Bundle)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor,boolean)
com.github.barteksc.pdfviewer.PDFView: int getCurrentPage()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getUserProperties(java.lang.String,java.lang.String,boolean,com.google.android.gms.internal.measurement.zzdo)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
com.google.firebase.datatransport.TransportRegistrar: TransportRegistrar()
com.shockwave.pdfium.PdfiumCore: java.lang.Long nativeGetSiblingBookmark(long,long)
com.google.firebase.components.ComponentDiscoveryService: ComponentDiscoveryService()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController2(io.flutter.plugin.platform.PlatformViewsController2)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroup(android.app.Notification$Builder,java.lang.String)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void register(android.media.AudioTrack)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void endAdUnitExposure(java.lang.String,long)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getTracingController()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushOpacity(float)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void onDragEnd(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getActiveNotifications(io.flutter.plugin.common.MethodChannel$Result)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: java.lang.String getName()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancelAllPendingNotifications(io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getDefault()
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void registerAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback,android.os.Handler)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.ServiceWorkerController getServiceWorkerController()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setInstanceIdProvider(com.google.android.gms.internal.measurement.zzdu)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void startForegroundService(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.media.app.NotificationCompat$Api34Impl: android.app.Notification$MediaStyle setRemotePlaybackInfo(android.app.Notification$MediaStyle,java.lang.CharSequence,int,android.app.PendingIntent,java.lang.Boolean)
androidx.media3.common.AudioAttributes$Api32: void setSpatializationBehavior(android.media.AudioAttributes$Builder,int)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setVibrationPattern(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.util.List castList(java.lang.Class,java.util.Collection)
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api24Impl: android.app.Notification$MessagingStyle$Message createMessage(java.lang.CharSequence,long,java.lang.CharSequence)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readGroupingInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
com.google.firebase.analytics.FirebaseAnalytics: com.google.firebase.analytics.FirebaseAnalytics getInstance(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupNotificationChannel(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails)
com.dexterous.flutterlocalnotifications.models.PersonDetails: PersonDetails(java.lang.Boolean,java.lang.Object,com.dexterous.flutterlocalnotifications.models.IconSource,java.lang.Boolean,java.lang.String,java.lang.String,java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onNewIntent(android.content.Intent)
androidx.core.app.NotificationCompatBuilder$Api20Impl: java.lang.String getGroup(android.app.Notification)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: ScheduledNotificationRepeatFrequency(java.lang.String,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
com.google.android.gms.measurement.internal.zzjh: com.google.android.gms.measurement.internal.zzjh[] values()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setBlockNetworkLoads(boolean)
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
com.google.android.gms.measurement.internal.zzak: com.google.android.gms.measurement.internal.zzak[] values()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.work.impl.background.systemjob.SystemJobService$Api31Impl: int getStopReason(android.app.job.JobParameters)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
com.google.android.gms.internal.measurement.zzjp: com.google.android.gms.internal.measurement.zzjp[] values()
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface: java.lang.String[] getSupportedFeatures()
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: FlutterFirebaseCoreRegistrar()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.GeolocationPermissions getGeoLocationPermissions()
com.github.barteksc.pdfviewer.PDFView$State: com.github.barteksc.pdfviewer.PDFView$State valueOf(java.lang.String)
androidx.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface: android.webkit.WebResourceResponse shouldInterceptRequest(android.webkit.WebResourceRequest)
androidx.core.app.NotificationCompat$BubbleMetadata$Api30Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void setCurrentPlayTime(android.animation.AnimatorSet,long)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
com.shockwave.pdfium.PdfiumCore: long nativeOpenMemDocument(byte[],java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
com.google.android.gms.measurement.AppMeasurement: void beginAdUnitExposure(java.lang.String)
com.google.android.gms.internal.measurement.zzmz: com.google.android.gms.internal.measurement.zzmz[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.Person buildPerson(android.content.Context,com.dexterous.flutterlocalnotifications.models.PersonDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void saveScheduledNotifications(android.content.Context,java.util.ArrayList)
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: void onReceive(android.content.Context,android.content.Intent)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setSmallIcon(android.app.Notification$Builder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Action$Builder setAllowGeneratedReplies(android.app.Notification$Action$Builder,boolean)
com.github.barteksc.pdfviewer.PDFView: com.github.barteksc.pdfviewer.util.FitPolicy getPageFitPolicy()
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
com.github.barteksc.pdfviewer.PDFView: int getSpacingPx()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getTestFlag(com.google.android.gms.internal.measurement.zzdo,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
com.google.gson.internal.bind.TypeAdapters$9: TypeAdapters$9()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver: ForceStopRunnable$BroadcastReceiver()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.HashMap getMappedNotificationChannel(android.app.NotificationChannel)
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedSchedule(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void createNotificationChannel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addRemoteInput(android.app.Notification$Action$Builder,android.app.RemoteInput)
androidx.work.impl.WorkDatabase: WorkDatabase()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
io.flutter.embedding.engine.FlutterJNI: boolean nativeIsSurfaceControlEnabled(long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setVisibility(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.PendingIntent getBroadcastPendingIntent(android.content.Context,int,android.content.Intent)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getUserAgentMetadataMap()
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: NotificationChannelDetails()
androidx.work.impl.workers.ConstraintTrackingWorker: ConstraintTrackingWorker(android.content.Context,androidx.work.WorkerParameters)
android.support.v4.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setUserAgentMetadataFromMap(java.util.Map)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] $values()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
com.google.gson.internal.bind.TypeAdapters$12: TypeAdapters$12()
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessageWithPayload(java.lang.reflect.InvocationHandler)
androidx.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface: void onMessage(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
com.github.barteksc.pdfviewer.PDFView: void setMaxZoom(float)
androidx.work.impl.diagnostics.DiagnosticsReceiver: DiagnosticsReceiver()
androidx.work.impl.background.systemjob.SystemJobService: SystemJobService()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerWebSettings()
androidx.media.app.NotificationCompat$Api21Impl: void setMediaSession(android.app.Notification$MediaStyle,android.media.session.MediaSession$Token)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forIncomingCall(android.app.Person,android.app.PendingIntent,android.app.PendingIntent)
kotlin.random.Random: Random()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initForTests(java.util.Map)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: com.google.common.collect.ImmutableList getDirectPlaybackSupportedEncodings(androidx.media3.common.AudioAttributes)
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void unregisterAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback)
androidx.core.app.NotificationCompat$MessagingStyle$Api28Impl: android.app.Notification$MessagingStyle createMessagingStyle(android.app.Person)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void unregisterOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzdp)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: java.lang.String getVariationsHeader()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
dev.fluttercommunity.plus.share.SharePlusPendingIntent: SharePlusPendingIntent()
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
com.shockwave.pdfium.PdfiumCore: void nativeClosePage(long)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation getDefaultStyleInformation(java.util.Map)
androidx.media.AudioAttributesImplApi21: AudioAttributesImplApi21()
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface: boolean isRedirect()
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
androidx.media.AudioManagerCompat$Api26Impl: int requestAudioFocus(android.media.AudioManager,android.media.AudioFocusRequest)
com.shockwave.pdfium.PdfiumCore: void nativeRenderPageBitmap(long,android.graphics.Bitmap,int,int,int,int,int,boolean)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertSafeBrowsingResponse(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getBackForwardCacheEnabled()
com.google.gson.internal.bind.TypeAdapters$24: TypeAdapters$24()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setTimeoutAfter(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.embedding.engine.FlutterJNI: void applyTransactions()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin: FlutterFirebaseCorePlugin()
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,android.app.Person)
com.github.barteksc.pdfviewer.PDFView: void setNightMode(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNextNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.app.ActivityCompat$Api31Impl: boolean isLaunchedFromBubble(android.app.Activity)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void rescheduleNotifications(android.content.Context)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getDisabledActionModeMenuItems()
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: int getCacheMode()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSessionTimeoutDuration(long)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertCookieManager(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setEventInterceptor(com.google.android.gms.internal.measurement.zzdp)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer,boolean)
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
com.google.gson.internal.bind.SerializationDelegatingTypeAdapter: SerializationDelegatingTypeAdapter()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
com.shockwave.pdfium.PdfiumCore: long nativeOpenDocument(int,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType[] values()
com.shockwave.pdfium.PdfDocument$Bookmark: PdfDocument$Bookmark()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
com.google.android.gms.internal.measurement.zzcj: com.google.android.gms.internal.measurement.zzcj[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioCapabilities getCapabilitiesInternalForDirectPlayback(android.media.AudioManager,androidx.media3.common.AudioAttributes)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void registerPlugin(java.lang.String,io.flutter.plugins.firebase.core.FlutterFirebasePlugin)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State[] values()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
androidx.core.app.NotificationCompat$CallStyle$Api28Impl: android.os.Parcelable castToParcelable(android.app.Person)
com.github.barteksc.pdfviewer.PDFView: void setMinZoom(float)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setSmallIcon(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.media3.exoplayer.video.VideoFrameReleaseHelper$Api30: void setSurfaceFrameRate(android.view.Surface,float)
androidx.fragment.app.DefaultSpecialEffectsController$Api24Impl: long totalDuration(android.animation.AnimatorSet)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setCacheMode(int)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getAdjustedDefault()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getMaxUserProperties(java.lang.String,com.google.android.gms.internal.measurement.zzdo)
com.github.barteksc.pdfviewer.PDFView: void setFitEachPage(boolean)
androidx.work.impl.foreground.SystemForegroundService$Api29Impl: void startForeground(android.app.Service,int,android.app.Notification,int)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
io.flutter.plugins.imagepicker.ImagePickerPlugin: ImagePickerPlugin()
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
com.google.android.gms.common.ErrorDialogFragment: ErrorDialogFragment()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.google.gson.internal.bind.TypeAdapters$26: TypeAdapters$26()
androidx.activity.Api34Impl: float touchX(android.window.BackEvent)
com.github.barteksc.pdfviewer.PDFView: com.shockwave.pdfium.PdfDocument$Meta getDocumentMeta()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSgtmDebugInfo(android.content.Intent)
androidx.room.MultiInstanceInvalidationService: MultiInstanceInvalidationService()
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void setBigPicture(android.app.Notification$BigPictureStyle,android.graphics.drawable.Icon)
com.google.gson.internal.bind.ArrayTypeAdapter$1: ArrayTypeAdapter$1()
com.github.barteksc.pdfviewer.PDFView: void setPageSnap(boolean)
com.github.barteksc.pdfviewer.PDFView: float getCurrentXOffset()
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Action$Builder setContextual(android.app.Notification$Action$Builder,boolean)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancelNotification(java.lang.Integer,java.lang.String)
com.shockwave.pdfium.PdfiumCore: java.lang.String nativeGetLinkURI(long,long)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: byte[] getAsArrayBuffer()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.PersonDetails readPersonDetails(java.util.Map)
com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar: FirebaseMessagingKtxRegistrar()
androidx.core.app.RemoteInput$Api20Impl: android.os.Bundle getResultsFromIntent(android.content.Intent)
com.google.firebase.messaging.threads.ThreadPriority: com.google.firebase.messaging.threads.ThreadPriority[] values()
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
com.google.common.collect.AbstractMultimap: AbstractMultimap()
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String getType(android.net.Uri)
com.github.barteksc.pdfviewer.PDFView: float getZoom()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: FlutterFirebasePluginRegistry()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setLights(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDark(int)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] values()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.work.impl.foreground.SystemForegroundService: SystemForegroundService()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSpeculativeLoadingStatus(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] $values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getNotificationAppLaunchDetails(io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromEngine(io.flutter.embedding.engine.plugins.FlutterPlugin$FlutterPluginBinding)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource valueOf(java.lang.String)
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface: java.lang.Object getOrCreatePeer(java.util.concurrent.Callable)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setOffscreenPreRaster(boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver: void onReceive(android.content.Context,android.content.Intent)
android.support.v4.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] values()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readColor(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler getMessagePayload()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: android.app.Notification$Action$Builder createActionBuilder(android.graphics.drawable.Icon,java.lang.CharSequence,android.app.PendingIntent)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.ArrayList loadScheduledNotifications(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowContentAccess(boolean)
com.google.android.gms.measurement.AppMeasurement: void logEventInternal(java.lang.String,java.lang.String,android.os.Bundle)
android.support.v4.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: android.view.SurfaceControl$Transaction createTransaction()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDataCollectionEnabled(boolean)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
com.google.gson.internal.bind.DefaultDateTypeAdapter$1: DefaultDateTypeAdapter$1()
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void repeatNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: FlutterLocalNotificationsPlugin()
androidx.core.app.NotificationCompat$CallStyle$Api21Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void showOverlaySurface2()
androidx.core.app.RemoteInput$Api29Impl: int getEditChoicesBeforeSending(java.lang.Object)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
com.github.barteksc.pdfviewer.PDFView: void setPageFling(boolean)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: byte[] castObjectToByteArray(java.lang.Object)
androidx.work.impl.utils.Api28Impl: java.lang.String getProcessName()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setDisabledActionModeMenuItems(int)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api29: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding[] values()
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver: ActionBroadcastReceiver()
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.activity.Api34Impl: android.window.BackEvent createOnBackEvent(float,float,float,int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder addAction(android.app.Notification$Builder,android.app.Notification$Action)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerTrigger(android.net.Uri,kotlin.coroutines.Continuation)
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api24Impl: android.app.Notification$MessagingStyle$Message setData(android.app.Notification$MessagingStyle$Message,java.lang.String,android.net.Uri)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getAppInstanceId(com.google.android.gms.internal.measurement.zzdo)
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder createBuilder(int,java.lang.CharSequence,android.app.PendingIntent)
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void areNotificationsEnabled(io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] $values()
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestFullScreenIntentPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.work.impl.utils.NetworkApi24: void registerDefaultNetworkCallbackCompat(android.net.ConnectivityManager,android.net.ConnectivityManager$NetworkCallback)
com.google.firebase.installations.FirebaseInstallationsRegistrar: com.google.firebase.installations.FirebaseInstallationsApi lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
androidx.work.impl.workers.DiagnosticsWorker: DiagnosticsWorker(android.content.Context,androidx.work.WorkerParameters)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.activity.Api34Impl: float progress(android.window.BackEvent)
io.flutter.embedding.engine.FlutterJNI: void swapTransactions()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress[] values()
androidx.core.app.NotificationCompat$MessagingStyle$Api26Impl: android.app.Notification$MessagingStyle addHistoricMessage(android.app.Notification$MessagingStyle,android.app.Notification$MessagingStyle$Message)
com.google.gson.internal.bind.TypeAdapters$5: TypeAdapters$5()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setGroupAlertBehavior(android.app.Notification$Builder,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRendererClient()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
com.google.gson.internal.bind.TypeAdapters$10: TypeAdapters$10()
vn.hunghd.flutterdownloader.DownloadedFileProvider: DownloadedFileProvider()
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency valueOf(java.lang.String)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.work.impl.background.systemalarm.Alarms$Api19Impl: void setExact(android.app.AlarmManager,int,long,android.app.PendingIntent)
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api28Impl: android.app.Notification$MessagingStyle$Message createMessage(java.lang.CharSequence,long,android.app.Person)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setRemoteInputHistory(android.app.Notification$Builder,java.lang.CharSequence[])
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void postMessageToMainFrame(java.lang.reflect.InvocationHandler,android.net.Uri)
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: DefaultStyleInformation(java.lang.Boolean,java.lang.Boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRenderer()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService: FlutterFirebaseMessagingService()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initialize(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzdw,long)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
com.google.android.gms.internal.measurement.zzbx: boolean onTransact(int,android.os.Parcel,android.os.Parcel,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: boolean isMultiProcessEnabled()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.util.List getAllProfileNames()
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannelGroups(android.app.NotificationManager,java.util.List)
androidx.work.impl.workers.CombineContinuationsWorker: CombineContinuationsWorker(android.content.Context,androidx.work.WorkerParameters)
com.dexterous.flutterlocalnotifications.models.NotificationAction: NotificationAction(java.util.Map)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin: FlutterAndroidLifecyclePlugin()
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice valueOf(java.lang.String)
com.google.firebase.messaging.FirebaseMessagingRegistrar: com.google.firebase.messaging.FirebaseMessaging lambda$getComponents$0(com.google.firebase.components.Qualified,com.google.firebase.components.ComponentContainer)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setActivity(android.app.Activity)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setCategory(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.work.impl.background.systemjob.SystemJobService$Api24Impl: android.net.Uri[] getTriggeredContentUris(android.app.job.JobParameters)
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails fromNotificationDetails(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebViewClient getWebViewClient()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomContentView(android.app.Notification$Builder,android.widget.RemoteViews)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerSource(android.net.Uri,android.view.InputEvent,kotlin.coroutines.Continuation)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannels(android.app.NotificationManager,java.util.List)
com.google.gson.internal.bind.EnumTypeAdapter$1: EnumTypeAdapter$1()
com.google.gson.internal.bind.JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory: JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float getFinalOpacity()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getCallbackHandle(io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.graphics.Bitmap getBitmapFromSource(android.content.Context,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource)
com.google.android.gms.measurement.AppMeasurement: java.util.List getConditionalUserProperties(java.lang.String,java.lang.String)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
com.github.barteksc.pdfviewer.PDFView$State: com.github.barteksc.pdfviewer.PDFView$State[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Boolean canCreateNotificationChannel(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails)
com.dexterous.flutterlocalnotifications.models.BitmapSource: BitmapSource(java.lang.String,int)
vn.hunghd.flutterdownloader.FlutterDownloaderPlugin: FlutterDownloaderPlugin()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestNotificationPolicyAccess(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupAllowWhileIdleAlarm(com.dexterous.flutterlocalnotifications.models.NotificationDetails,android.app.AlarmManager,long,android.app.PendingIntent)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void generateEventId(com.google.android.gms.internal.measurement.zzdo)
io.flutter.embedding.engine.FlutterJNI: void hideOverlaySurface2()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
com.github.barteksc.pdfviewer.PDFView: int getPageCount()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.ParcelFileDescriptor openFile(android.content.ContentProvider,android.net.Uri)
com.dexterous.flutterlocalnotifications.models.Time: Time()
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setColorized(android.app.Notification$Builder,boolean)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
com.google.common.collect.AbstractMapEntry: AbstractMapEntry()
com.google.android.gms.measurement.AppMeasurementJobService: AppMeasurementJobService()
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
