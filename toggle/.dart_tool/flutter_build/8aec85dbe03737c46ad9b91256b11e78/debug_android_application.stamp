{"inputs": ["/Users/<USER>/Desktop/Toggle/toggle/.dart_tool/flutter_build/8aec85dbe03737c46ad9b91256b11e78/app.dill", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/Desktop/Toggle/toggle/pubspec.yaml", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/home_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/skip.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_audio_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_filter.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/course_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/radio_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_lock.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/course_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_right_arrow.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/course_active2.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/favourites_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/radio_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_certificates.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_delete_account.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/favourites_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_notification.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/icon_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_arrow_left.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/home_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/correct.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_inactive_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/wrong.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/close.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/more_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/flag_us.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_sort.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/flag_bn.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/more_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_image_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/icon_bell.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_video_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_note_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/flag_sa.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/app_name_logo.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_edit.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_star.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/timer.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_logout.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/svg/ic_arrow_right_svg.svg", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_certificate_lock.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/support.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/check_otp.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/home_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_download_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_support.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/location_pin.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_category_demo_1.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_camera.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/failed.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_rate.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/spinner.gif", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/togglename.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_play_circle.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/Toggle_logo_gif.gif", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/privacy.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/blog.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_demo_user_1.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/share.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/im_course_demo.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_certificate_checked.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/phonecall.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/ic_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/faq.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/toggle_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/auth_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/exam.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/no_internet_connection.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/password_recover.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/app_name_logo_light.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/pass.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/t_c.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/profile_background.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/toggleVersion_2.mp4", "/Users/<USER>/Desktop/Toggle/toggle/assets/images/app_name_logo_dark.png", "/Users/<USER>/Desktop/Toggle/toggle/assets/font/Lexend-Light.ttf", "/Users/<USER>/Desktop/Toggle/toggle/assets/font/Lexend-Regular.ttf", "/Users/<USER>/Desktop/Toggle/toggle/assets/font/Lexend-Medium.ttf", "/Users/<USER>/Desktop/Toggle/toggle/assets/font/Lexend-Bold.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/assets/no_sleep.js", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/Toggle/toggle/.dart_tool/flutter_build/8aec85dbe03737c46ad9b91256b11e78/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.46/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ansicolor-2.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-6.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.23/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.8.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/color-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_wrapper-1.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dartx-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_frame-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_preview-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.5.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/download-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.8.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.48/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_downloader-1.11.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_easyloading-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_form_builder-10.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_gen_core-5.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_gen_runner-5.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0-beta.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.21/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.10+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_widget_from_html-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_widget_from_html_core-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/form_builder_validators-11.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_cached_network_image-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_chewie-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_just_audio-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_svg-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_url_launcher-0.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fwfh_webview-0.15.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hashcodes-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_size_getter-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl_utils-2.8.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.43/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/list_counter-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pretty_dio_logger-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/time-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.11+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.11+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.11+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.2.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-0.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.2/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "/Users/<USER>/Desktop/Toggle/toggle/DOES_NOT_EXIST_RERUN_FOR_WILDCARD696254190"], "outputs": ["/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/home_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/skip.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_audio_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_filter.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/radio_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_lock.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_right_arrow.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_active2.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/favourites_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/radio_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_certificates.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_delete_account.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/favourites_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_notification.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/icon_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_arrow_left.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/home_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/correct.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_inactive_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/wrong.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/close.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/more_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_us.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_sort.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_bn.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/more_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_image_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/icon_bell.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_video_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_note_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_sa.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/app_name_logo.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_edit.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_star.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/timer.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_logout.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_arrow_right_svg.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_certificate_lock.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/support.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/check_otp.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/home_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_download_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_support.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/location_pin.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_category_demo_1.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_camera.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/failed.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_rate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/spinner.gif", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/togglename.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_play_circle.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/Toggle_logo_gif.gif", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/privacy.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/blog.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_demo_user_1.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/share.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_course_demo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_certificate_checked.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/phonecall.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/faq.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/toggle_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/auth_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/exam.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/no_internet_connection.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/password_recover.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/app_name_logo_light.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/pass.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/t_c.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/profile_background.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/toggleVersion_2.mp4", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/app_name_logo_dark.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Light.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Regular.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Medium.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Bold.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/packages/wakelock_plus/assets/no_sleep.js", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]}