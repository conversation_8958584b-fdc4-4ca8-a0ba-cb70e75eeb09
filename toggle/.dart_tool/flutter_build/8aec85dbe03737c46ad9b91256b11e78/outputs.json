["/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/home_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/skip.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_audio_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_filter.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/radio_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_lock.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_right_arrow.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/course_active2.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/favourites_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/radio_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_certificates.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_delete_account.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/favourites_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_notification.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/icon_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_arrow_left.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/home_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/correct.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_inactive_heart.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/wrong.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/close.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/more_active.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_us.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_sort.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_bn.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/more_inactive.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_image_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/icon_bell.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_video_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_note_file.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_search.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/flag_sa.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/app_name_logo.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_edit.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_star.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/timer.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_logout.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/svg/ic_arrow_right_svg.svg", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_certificate_lock.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/support.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/check_otp.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/home_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_download_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_support.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/location_pin.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_category_demo_1.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_camera.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/failed.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_rate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/spinner.gif", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_certificate.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/togglename.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_play_circle.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/Toggle_logo_gif.gif", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/privacy.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/blog.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_demo_user_1.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/share.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/im_course_demo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_certificate_checked.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/phonecall.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/ic_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/faq.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/toggle_logo.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/auth_welcome_avt.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/exam.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/no_internet_connection.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/password_recover.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/app_name_logo_light.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/pass.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/t_c.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/profile_background.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/toggleVersion_2.mp4", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/images/app_name_logo_dark.png", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Light.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Regular.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Medium.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/assets/font/Lexend-Bold.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/packages/wakelock_plus/assets/no_sleep.js", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/Toggle/toggle/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]